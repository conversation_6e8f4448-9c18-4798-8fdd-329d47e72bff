<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:esa-rpc="http://esastack.io/schema/esa-rpc"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://esastack.io/schema/esa-rpc http://esastack.io/schema/esa-rpc/esa-rpc.xsd">

    <esa-rpc:service interface="com.oppo.browser.video.common.service.video.IVideoSourceAdapter" ref="iVideoSourceAdapter"
        protocol="dubbo" group="youli"/>
    <bean id="iVideoSourceAdapter" class="com.heytap.video.youli.service.impl.IVideoYouliAdapterImpl" />

    <esa-rpc:service interface="com.oppo.browser.video.common.service.video.IVideoYouli" ref="iVideoYouli"
                   protocol="dubbo"/>
    <bean id="iVideoYouli" class="com.heytap.video.youli.service.impl.IVideoYouliImpl" />
    <esa-rpc:registry protocol="euler-multi">
        <!--同时注册到esa以及polaris注册中心-->
        <esa-rpc:parameter key="service-registry" value="polaris,esa"/>
        <!--使用esa注册中心发现服务-->
        <esa-rpc:parameter key="reference-registry" value="esa"/>
    </esa-rpc:registry>
</beans>