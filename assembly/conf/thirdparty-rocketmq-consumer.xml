<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:video="http://com.heytap.cpc/schema/video"
  xsi:schemaLocation="http://www.springframework.org/schema/beans
http://www.springframework.org/schema/beans/spring-beans-3.0.xsd http://com.heytap.cpc/schema/video http://com.heytap.cpc/schema/video/video.xsd">

  <bean id="thirdpartyMQPushConsumerOrderly" class="com.oppo.cpc.video.framework.lib.thirdparty.mq.ThirdpartyMQPushConsumer"
    init-method="start" destroy-method="shutdown">
    <constructor-arg index="0" value="${rocketmq.consumer.orderly.consumerGroup}"/>
    <constructor-arg index="1" value="true"/>

    <property name="consumeFromWhere">
      <value type="org.apache.rocketmq.common.consumer.ConsumeFromWhere">CONSUME_FROM_FIRST_OFFSET</value>
    </property>
    <property name="namesrvAddr" value="${rocketmq.consumer.namesrvAddr}"/>
    <property name="subscribeTopics">
     <list>
       <video:topic name="${rocketmq.consumer.like.favorite.topic}" tag="youli" serviceType="likeFavorite" reconsumeIfException="false" />
     </list>
    </property>

    <property name="consumeThreadMin" value="${rocketmq.consumer.orderly.consumeThreadMin}"/>
    <property name="consumeThreadMax" value="${rocketmq.consumer.orderly.consumeThreadMax}"/>
    <property name="pullInterval" value="${rocketmq.consumer.orderly.pullInterval}"/>
    <property name="consumeMessageBatchMaxSize" value="${rocketmq.consumer.orderly.consumeMessageBatchMaxSize}"/>
    <property name="maxReconsumeTimes" value="${rocketmq.consumer.orderly.maxReconsumeTimes}"/>
    <property name="clientCallbackExecutorThreads" value="${rocketmq.consumer.orderly.clientCallbackExecutorThreads}"/>
    <property name="pollNameServerInterval" value="${rocketmq.consumer.orderly.pollNameServerInterval}"/>
    <property name="heartbeatBrokerInterval" value="${rocketmq.consumer.orderly.heartbeatBrokerInterval}"/>
    <property name="persistConsumerOffsetInterval" value="${rocketmq.consumer.orderly.persistConsumerOffsetInterval}"/>
    <property name="enableMsgTrace" value="true"/>
  </bean>

</beans>