<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:esa-rpc="http://esastack.io/schema/esa-rpc"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://esastack.io/schema/esa-rpc http://esastack.io/schema/esa-rpc/esa-rpc.xsd">


    <esa-rpc:reference id="standardAlbumRpcApi"
                       interface="com.heytap.longvideo.client.media.StandardAlbumRpcApi"
                       providerAppId="longvideo-media-service"
                       protocol="dubbo" retries="1" check="false"
                       zoneCode="${esarpc.virtualProgramRelationRpcApi.zoneCode}">
        <esa-rpc:method name="getBySidsFilterInvalid" async="true"/>
    </esa-rpc:reference>

    <esa-rpc:reference id="mediaCommonServiceRpcApi"
                       interface="com.heytap.longvideo.client.media.MediaCommonServiceRpcApi"
                       providerAppId="longvideo-media-service"
                       protocol="dubbo" retries="1" check="false"
                       zoneCode="${esarpc.virtualProgramRelationRpcApi.zoneCode}">
        <esa-rpc:method name="fetchFunshionAccessToken" async="true"/>
    </esa-rpc:reference>

    <esa-rpc:reference id="standardVideoRpcApi"
                       interface="com.heytap.longvideo.client.media.StandardVideoRpcApi"
                       providerAppId="longvideo-media-service"
                       protocol="dubbo" retries="1" check="false"
                       zoneCode="${esarpc.virtualProgramRelationRpcApi.zoneCode}">
        <esa-rpc:method name="getByVids" async="true"/>
    </esa-rpc:reference>

    <esa-rpc:reference id="virtualProgramRelationRpcApi"
                       interface="com.heytap.longvideo.client.media.VirtualProgramRelationRpcApi"
                       providerAppId="longvideo-media-service"
                       protocol="dubbo" retries="1" check="false"
                       zoneCode="${esarpc.virtualProgramRelationRpcApi.zoneCode}">
        <esa-rpc:method name="queryBySid" async="true"/>
    </esa-rpc:reference>

    <esa-rpc:reference id="ugcStandardVideoRpcApi"
                       interface="com.heytap.longvideo.client.media.UgcStandardVideoRpcApi"
                       providerAppId="longvideo-media-service"
                       protocol="dubbo" retries="1" check="false"
                       zoneCode="${esarpc.virtualProgramRelationRpcApi.zoneCode}">
        <esa-rpc:method name="getByVidList" async="true"/>
    </esa-rpc:reference>

    <esa-rpc:reference id="ugcStandardAuthorRpcApi"
                       interface="com.heytap.longvideo.client.media.UgcStandardAuthorRpcApi"
                       providerAppId="longvideo-media-service"
                       protocol="dubbo" retries="1" check="false"
                       zoneCode="${esarpc.virtualProgramRelationRpcApi.zoneCode}">
        <esa-rpc:method name="getBySidList" async="true"/>
    </esa-rpc:reference>

    <esa-rpc:reference id="standardTrailerRpcApi"
                       interface="com.heytap.longvideo.client.media.StandardTrailerRpcApi"
                       providerAppId="longvideo-media-service"
                       protocol="dubbo" retries="1" check="false"
                       zoneCode="${esarpc.standardTrailerRpcApi.zoneCode}">
        <esa-rpc:method name="queryBy" async="true"/>
    </esa-rpc:reference>

    <esa-rpc:reference id="lvTrailerOperationRpcApi"
                       interface="com.heytap.longvideo.client.arrange.LvTrailerOperationRpcApi"
                       providerAppId="longvideo-arrange-service"
                       protocol="dubbo" retries="1" check="false"
                       zoneCode="${esarpc.lvTrailerOperationRpcApi.zoneCode}">
        <esa-rpc:method name="trailerOperationData" async="true"/>
        <esa-rpc:method name="findLvTrailerOperationSetByCodes" async="true"/>
    </esa-rpc:reference>
</beans>