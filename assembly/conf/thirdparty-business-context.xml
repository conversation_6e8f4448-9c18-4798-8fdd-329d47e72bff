<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">
    <!--<bean id="counterBase" class="com.oppo.browser.common.app.lib.monitor.CounterBase" init-method="init" />-->

    <bean id="CONSUME_FROM_FIRST_OFFSET" class="org.springframework.beans.factory.config.FieldRetrievingFactoryBean">
        <property name="staticField" value="org.apache.rocketmq.common.consumer.ConsumeFromWhere.CONSUME_FROM_FIRST_OFFSET" />
    </bean>

    <bean id="myDefaultMessageListenerConcurrently" class="com.oppo.cpc.video.framework.lib.rocketmq.MyDefaultMessageListenerConcurrently">
    </bean>

    <bean id="myDefaultMQPushConsumerConcurrently" class="com.oppo.cpc.video.framework.lib.rocketmq.MyDefaultMQPushConsumer"
        init-method="start" destroy-method="shutdown">
        <constructor-arg index="0" value="${rocketmq.consumer.videolog.consumerGroup}" />
        <constructor-arg index="1" ref="myDefaultMessageListenerConcurrently" />

        <property name="consumeFromWhere" ref="CONSUME_FROM_FIRST_OFFSET" />
        <property name="namesrvAddr" value="${rocketmq.consumer.namesrvAddr}" />
        <property name="subscription">
            <map>
                <entry key="${rocketmq.consumer.video.hotsoon.topic}" value="${rocketmq.consumer.videolog.subExpression}" />
                <entry key="${rocketmq.consumer.video.horizon.topic}" value="${rocketmq.consumer.videolog.subExpression}" />
            </map>
        </property>

        <property name="consumeThreadMin" value="${rocketmq.consumer.consumeThreadMin}" />
        <property name="consumeThreadMax" value="${rocketmq.consumer.consumeThreadMax}" />
        <property name="pullInterval" value="${rocketmq.consumer.pullInterval}" />
        <property name="consumeMessageBatchMaxSize" value="${rocketmq.consumer.consumeMessageBatchMaxSize}" />
        <property name="maxReconsumeTimes" value="${rocketmq.consumer.maxReconsumeTimes}" />
        <property name="clientCallbackExecutorThreads" value="${rocketmq.consumer.clientCallbackExecutorThreads}" />
        <property name="pollNameServerInterval" value="${rocketmq.consumer.pollNameServerInterval}" />
        <property name="heartbeatBrokerInterval" value="${rocketmq.consumer.heartbeatBrokerInterval}" />
        <property name="persistConsumerOffsetInterval" value="${rocketmq.consumer.persistConsumerOffsetInterval}" />
    </bean>

    <bean id="esaConsumeMessageTraceHookImpl" class="com.oppo.trace.rocketmq4.TraceConsumeMessageTraceHookImpl" />

    <bean id="springContextUtils" class="esa.servicekeeper.ext.factory.spring.utils.SpringContextUtils" />

    <bean id="registerConsumeMessageHook" class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject">
            <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
                <property name="targetObject" ref="myDefaultMQPushConsumerConcurrently" />
                <property name="targetMethod">
                    <value>getDefaultMQPushConsumerImpl</value>
                </property>
            </bean>
        </property>
        <property name="targetMethod" value="registerConsumeMessageHook" />
        <property name="arguments">
            <list>
                <ref bean="esaConsumeMessageTraceHookImpl" />
            </list>
        </property>
    </bean>
</beans>