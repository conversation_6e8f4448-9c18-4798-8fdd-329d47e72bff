#!/bin/sh

#set HOME
cd `dirname "$0"`/..
export APPLICATION_HOME=`pwd`
cd $APPLICATION_HOME
if [ -z "$APPLICATION_HOME" ] ; then
    echo
    echo "Error: APPLICATION_HOME environment variable is not defined correctly."
    echo
    exit 1
fi
#==============================================================================

source /etc/profile
source $(dirname $0)/../../env.sh

if [ "$PAAS_ENV"x = "test"x -o "$PAAS_ENV"x = "test-bj"x -o "$PAAS_ENV"x = "testbj"x ]; then
    env_iast_path=$(dirname $0)/plugin/iast_install.sh
    if [ -f $env_iast_path ]; then
        source $env_iast_path
    fi
fi

#check JAVA_HOME & java
noJavaHome=false
if [ -z "$JAVA_HOME" ] ; then
    noJavaHome=true
fi
if [ ! -e "$JAVA_HOME/bin/java" ] ; then
    noJavaHome=true
fi
if $noJavaHome ; then
    echo
    echo "Error: JAVA_HOME environment variable is not set."
    echo
    exit 1
fi
#==============================================================================
#==============================================================================
if [ "true" = "$REMOTE_DEBUG" ] ; then
    remoteDebug=" -Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=*:1357"
else
    remoteDebug=""
fi
#==============================================================================
export APPLICATION_PID=$APPLICATION_HOME/logs/APPLICATION_PID
export JAVA_OPTS="${HEY_JVM_OPTIONS}"

if [ ! -z "$APPLICATION_PID" ]; then
  if [ -f "$APPLICATION_PID" ]; then
    if [ -s "$APPLICATION_PID" ]; then
      echo "Existing PID file found during start."
      if [ -r "$APPLICATION_PID" ]; then
        PID=`cat "$APPLICATION_PID"`
        ps -p $PID >/dev/null 2>&1
        if [ $? -eq 0 ] ; then
          echo "Application appears to still be running with PID $PID. Start aborted."
          exit 1
        else
          echo "Removing/clearing stale PID file."
          rm -f "$APPLICATION_PID" >/dev/null 2>&1
          if [ $? != 0 ]; then
            if [ -w "$APPLICATION_PID" ]; then
              cat /dev/null > "$APPLICATION_PID"
            else
              echo "Unable to remove or clear stale PID file. Start aborted."
              exit 1
            fi
          fi
        fi
      else
        echo "Unable to read PID file. Start aborted."
        exit 1
      fi
    else
      rm -f "$APPLICATION_PID" >/dev/null 2>&1
      if [ $? != 0 ]; then
        if [ ! -w "$APPLICATION_PID" ]; then
          echo "Unable to remove or write to empty PID file. Start aborted."
          exit 1
        fi
      fi
    fi
  fi
fi

#set CLASSPATH
APPLICATION_CLASSPATH="$APPLICATION_HOME/disconf:$APPLICATION_HOME/conf:$APPLICATION_HOME/lib/classes"
for i in "$APPLICATION_HOME"/lib/*.jar
do
    APPLICATION_CLASSPATH="$APPLICATION_CLASSPATH:$i"
done
#==============================================================================

#startup Server
RUN_CMD="nohup \"$JAVA_HOME/bin/java\""
RUN_CMD="$RUN_CMD -Drocketmq.client.logUseSlf4j=\"true\""
RUN_CMD="$RUN_CMD -Dapplication.home=\"$APPLICATION_HOME\""
RUN_CMD="$RUN_CMD -Dcatalina.home=\"$APPLICATION_HOME\""
RUN_CMD="$RUN_CMD -classpath \"$APPLICATION_CLASSPATH\""
RUN_CMD="$RUN_CMD $JAVA_OPTS $remoteDebug"
RUN_CMD="$RUN_CMD $JAVA_IAST"
RUN_CMD="$RUN_CMD $JACOCO_OPTS"
RUN_CMD="$RUN_CMD com.heytap.video.thirdparty.bootstrap.Main $@"
RUN_CMD="$RUN_CMD >> \"$APPLICATION_HOME/logs/console.log\" 2>&1 &"
echo $RUN_CMD
eval $RUN_CMD
#==============================================================================

if [ ! -z "$APPLICATION_PID" ]; then
  echo $! > "$APPLICATION_PID"
fi

echo "Application started."