<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.0" xmlns:xsi="http://www.w3.org/2001/XMLSch ema-instance"
	xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.0 http://maven.apache.org/xsd/assembly-2.0.0.xsd">
	<formats>
		<format>tar.gz</format>
	</formats>
	<includeBaseDirectory>false</includeBaseDirectory>
	<fileSets>
		<fileSet>
			<directory>assembly/bin</directory>
			<outputDirectory>video-youli-service/bin</outputDirectory>
			<fileMode>0755</fileMode>
			<lineEnding>keep</lineEnding>
		</fileSet>
		<fileSet>
			<directory>assembly/conf</directory>
			<outputDirectory>video-youli-service/conf</outputDirectory>
		</fileSet>
		<fileSet>
			<directory>assembly/logs</directory>
			<outputDirectory>video-youli-service/logs</outputDirectory>
		</fileSet>
		<fileSet>
			<directory>assembly/lib</directory>
			<outputDirectory>video-youli-service/lib</outputDirectory>
		</fileSet>
	</fileSets>
	<dependencySets>
		<dependencySet>
			<outputDirectory>video-youli-service/lib</outputDirectory>
			<scope>runtime</scope>
		</dependencySet>
	</dependencySets>

</assembly>