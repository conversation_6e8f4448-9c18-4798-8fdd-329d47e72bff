# 文件说明：
## 1. 该文件是由持续集成平台根据用户在系统上的编译配置生成的编译文件，主要目的是将其作为项目的一部分也纳入git版本管控
## 2. 该文件放在项目的根目录下，流水线执行构建任务时，会直接执行该文件进行编译构建
## 3. 用户在本地可以运行git bash等终端，执行命令：sh columbus_build.sh 进行本地编译构建
## 4. 请不要修改脚本里的函数名称和结构，这可能会导致编译异常等各种本地云上编译结果不一致问题
## 5. 如果想修改编译命令，可直接修改 build_command里的内容
## 6. 脚本的生成和管控已在后端DTA上做过澄清，各业务的技术leader和主管已知晓
#############################################################################################################################
build_command() {
  build_command=(mvn clean package -Dmaven.test.skip=true)
}
# 如需编译根目录的子模块sub-module,那么配置为sub-module/pom.xml
add_pom_file_param() {
  build_command+=("-f" "pom.xml")
}
#############################################################################################################################


# 以下是用户不需要关注的部分，请不要修改这些内容。
#############################################################################################################################
# 不需要关注与变更，基础打印函数
print_info() {
  echo "[$(date "+%Y-%m-%d %H:%M:%S")] [SH][INFO] $1"
}
# 不需要关注与变更，基础打印函数
print_error() {
  echo -e "\033[31m[$(date "+%Y-%m-%d %H:%M:%S")] [SH][ERROR] $1\033[0m" >&2
}
# 需要关注，会检查执行编译命令的环境，与云上编译任务选择的"构建环境"镜像版本是一致的，如果变更，请到持续集成编译任务进行修改
check_environment() {
  print_info "正在检查JDK、MAVEN环境是否符合基础镜像环境版本"
  # 检查Java是否在PATH中
  if ! command -v java &> /dev/null; then
    print_error "当前环境中 Java 未安装或未在系统环境变量 PATH 中定义，请先下载对应版本的包并配置系统环境变量"
    exit 1
  fi
  # 检查Maven是否在PATH中
  if ! command -v mvn &> /dev/null; then
    print_error "当前环境中 Maven 未安装或未在系统环境变量 PATH 中定义，请先下载对应版本的包并配置系统环境变量"
    exit 1  
  fi
  # 检查Java版本
  local java_version
  java_version=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}')
  print_info "当前环境的java version:$java_version"
  if [[ ! "$java_version" =~ ^11 ]]; then
    print_error "当前的Java版本是 $java_version，要求的JDK版本是 11，请重新下载对应版本的包并重新配置系统环境变量"
    exit 1
  fi
  # 检查Maven版本
  local mvn_version
  mvn_version=$(mvn -version 2>&1 | awk -F '[ \"]' '/Apache Maven/ {print $3}')
  print_info "当前环境的Maven version:$mvn_version"
  if [[ ! "$mvn_version" =~ ^3 ]]; then
    print_error "当前环境的Maven版本是 $mvn_version，要求的Maven版本是 3.x，请重新下载对应版本的包并重新配置系统环境变量"
    exit 1
  fi
}
# 不需要关注，运行流水线会自行注入环境变量进行拼接，如果变更，请到持续集成编译任务进行修改
add_pipeline_command() {
  if [ -n "$WORKSPACE" ] && [ -n "$APP_NAME" ]; then
    build_command+=("dependency:tree")
    build_command+=("-DoutputFile=${WORKSPACE}/${APP_NAME}/dependency_tree.txt")
  fi
  if [ -n "$MAVEN_REPO_LOCAL" ]; then
    build_command+=("-Dmaven.repo.local=${MAVEN_REPO_LOCAL}")
  fi
}
# 不需要关注，真正执行编译命令
execute_command() {
  # 打印生成的命令（用于调试）
  print_info "最终生成的编译命令是: ${build_command[*]}，开始执行，以下是编译命令的日志"
  "${build_command[@]}"
  local cmd_exit_status=$?
  print_info "编译命令退出状态码:$cmd_exit_status,Linux shell规范定义返回0表示成功，返回非0表示失败"
  if [ $cmd_exit_status -ne 0 ]; then
    print_error "${build_command[*]} 编译命令执行失败，请根据日志进行调试。"
    exit 1
  fi
}
# 需要关注，检查编译后的最终产物是否存在指定的目录中，如果变更，请到持续集成编译任务进行修改
check_artifact() {
  if [ -z "$(find . -name \"*.tar\" -o -name \"*.tar.gz\")" ]; then
    print_error "在当前目录下找不到 *.tar 格式的产物，请根据以下排查："
    print_error "1. 编译失败没有成功生成编译产物 "
    print_error "2. 编译成功，但产物格式不为：tar"
    exit 1
  fi
  print_info "编译产物格式校验成功"
}
main() {
  # 需要关注，会检查执行编译命令的环境，与云上编译任务选择的"构建环境"镜像版本是一致的
  check_environment
  # 需要关注，如果需要变更编译命令直接再此处变更即可
  build_command
  # 不需要关注，运行流水线会自行注入环境变量进行拼接
  add_pipeline_command
  # 增加pom文件
  add_pom_file_param
  # 不需要关注，真正执行编译命令
  execute_command
  # 需要关注，检查编译后的最终产物是否存在指定的目录中
  check_artifact
}

main