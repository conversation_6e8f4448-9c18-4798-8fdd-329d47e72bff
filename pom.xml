<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <properties>
        <app.encoding>UTF-8</app.encoding>
        <app.version>1.0.0</app.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.build.timestamp.format>yyyyMMdd</maven.build.timestamp.format>
        <java.home>${env.JAVA_HOME}</java.home>
        <video-framework.version>0.0.48</video-framework.version>
    </properties>

    <groupId>com.heytap.video</groupId>
    <artifactId>com-heytap-video-youli-service</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>video-youli-service</name>
    <description>The project of video-youli-service</description>

    <!-- maven私服配置 -->
    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <name>nexus-releases</name>
            <url>http://nexus.os.adc.com/nexus/content/repositories/releases/</url>
        </repository>

        <snapshotRepository>
            <id>nexus-snapshots</id>
            <name>nexus-snapshots</name>
            <url>http://nexus.os.adc.com/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <pluginRepositories>
        <pluginRepository>
            <id>nexus-public</id>
            <url>http://nexus.os.adc.com/nexus/content/groups/public/</url>
        </pluginRepository>
    </pluginRepositories>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>esa</groupId>
                <artifactId>esa-rpc</artifactId>
                <version>1.1.7.GA</version>
                <exclusions>
                    <exclusion>
                        <artifactId>esa-rpc-config</artifactId>
                        <groupId>esa</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <artifactId>framework-models</artifactId>
            <groupId>com.heytap.video</groupId>
            <version>${video-framework.version}</version>
        </dependency>
        <dependency>
            <artifactId>framework-components-core</artifactId>
            <groupId>com.heytap.video</groupId>
            <version>${video-framework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>1.10.0</version>
        </dependency>

        <dependency>
            <groupId>com.heytap.video</groupId>
            <artifactId>com-heytap-video-common-lib</artifactId>
            <version>4.0.32</version>
            <exclusions>
                <exclusion>
                    <groupId>com.oppo.basic.heracles</groupId>
                    <artifactId>heracles-client</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>org.apache.dubbo</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-context</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-beans</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-aop</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-expression</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-core</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons</artifactId>
                    <groupId>esa</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-recipes</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-framework</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-client</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>framework-trace-dubbo271</artifactId>
                    <groupId>com.oppo</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>framework-models</artifactId>
                    <groupId>com.heytap.video</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.heytap.video</groupId>
            <artifactId>com-heytap-video-thirdparty-framework</artifactId>
            <version>1.0.15</version>
        </dependency>
        <dependency>
            <groupId>com.oppo.euler</groupId>
            <artifactId>euler-discovery-esarpc</artifactId>
            <version>1.1.35</version>
        </dependency>
        <dependency>
            <groupId>com.heytap.video</groupId>
            <artifactId>framework-metrics</artifactId>
            <version>0.0.36</version>
        </dependency>
        <!--heracles升级 依赖适配-->
        <dependency>
            <groupId>esa</groupId>
            <artifactId>sailor-sdk</artifactId>
            <version>1.2.5</version>
        </dependency>
        <dependency>
            <groupId>esa</groupId>
            <artifactId>servicekeeper-spring-adapter</artifactId>
            <version>1.5.7.RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>esa</groupId>
                    <artifactId>sailor-sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.heytap.media</groupId>
            <artifactId>longvideo-media-api</artifactId>
            <version>2.1.8</version>
            <exclusions>
                <exclusion>
                    <artifactId>framework-models</artifactId>
                    <groupId>com.heytap.video</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.heytap.longvideo</groupId>
            <artifactId>longvideo-arrange-client-lib</artifactId>
            <version>1.0.54</version>
        </dependency>

        <!--below just for unit test-->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>2.23.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-core</artifactId>
            <version>1.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-library</artifactId>
            <version>1.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>5.1.4.RELEASE</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>spring-core</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>1.4.200</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mock-server</groupId>
            <artifactId>mockserver-netty</artifactId>
            <version>5.11.1</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang3</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mock-server</groupId>
            <artifactId>mockserver-junit-rule</artifactId>
            <version>5.11.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>esa.rpc</groupId>
            <artifactId>dubbo-rpc-mock</artifactId>
            <version>1.0.9.BETA</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>commons-lang3</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-context</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-core</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>esa-rpc</artifactId>
                    <groupId>com.esa</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.stefanbirkner</groupId>
            <artifactId>system-rules</artifactId>
            <version>1.16.1</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>junit</artifactId>
                    <groupId>junit</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.oppo.basic.heracles</groupId>
            <artifactId>heracles-client</artifactId>
            <version>2.3.9</version>
        </dependency>

        <!-- PowerMock 依赖 -->
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>assembly/conf</directory>
            </resource>
            <resource>
                <directory>assembly/mapping</directory>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <finalName>video-youli-service-${app.version}-${maven.build.timestamp}</finalName>
                    <descriptors>
                        <descriptor>assembly/assembly.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.2</version>
                <configuration>
                    <verbose>true</verbose>
                    <overwrite>true</overwrite>
                </configuration>
            </plugin>

            <!--below just for unit test-->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.5</version>
                <configuration>
                    <!--排除不需要单元测试的文件-->
                    <excludes>
                        <exclude>**/bootstrap/*.class</exclude>
                        <exclude>**/cache/*.class</exclude>
                        <exclude>**/config/*.class</exclude>
                        <exclude>**/mapper/*.class</exclude>
                        <exclude>**/model/*.class</exclude>
                    </excludes>
                    <!--surefireArgLine：解决偶尔无法生成报告的问题-->
                    <propertyName>surefireArgLine</propertyName>
                </configuration>
                <executions>
                    <execution>
                        <id>default-prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <argLine>${surefireArgLine} -Dfile.encoding=UTF-8</argLine>
                    <forkCount>1</forkCount>
                    <reuseForks>false</reuseForks>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <configLocation>checkstyle.xml</configLocation>
                </configuration>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>