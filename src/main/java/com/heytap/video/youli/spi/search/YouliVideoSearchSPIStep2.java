package com.heytap.video.youli.spi.search;

import com.heytap.video.thirdparty.constant.ApiConfigNameEnum;
import com.heytap.video.thirdparty.spi.search.VideoSearchSPI;
import com.heytap.video.youli.biz.YouliResourceService;
import com.heytap.video.youli.model.ExtraInfo;
import com.heytap.video.youli.model.content.ResourceResult;
import com.heytap.video.youli.model.content.VideoResource;
import com.heytap.video.youli.model.search.SearchResult;
import com.heytap.video.youli.model.search.SearchVideoInfo;
import com.oppo.browser.common.app.lib.encryption.JsonTools;
import com.oppo.browser.strategy.model.AttributeValues;
import com.oppo.browser.video.common.pubobj.resource.ListBaseReqProperty;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.FeedsList;
import com.oppo.cpc.video.framework.lib.exception.InvalidDataRuntimeException;
import com.oppo.cpc.video.framework.lib.thirdparty.RequestMethod;
import com.oppo.cpc.video.framework.lib.thirdparty.SPIContext;
import com.oppo.cpc.video.framework.lib.thirdparty.VideoBaseRt;
import com.oppo.cpc.video.framework.lib.thirdparty.spi.AbstractYamlHttpSPI;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static com.oppo.cpc.video.framework.lib.thirdparty.constant.OutCodeConstant.THIRD_PARTY_API_RESP_EXCEPTION;

/**
 * youli源视频搜索步骤2,根据视频id获取视频内容
 *
 * <AUTHOR>
 */
@Service
public class YouliVideoSearchSPIStep2 extends AbstractYamlHttpSPI<ListBaseReqProperty, FeedsList, SearchResult>
        implements VideoSearchSPI<FeedsList, SearchResult> {
    @Autowired
    private YouliResourceService youliResourceService;

    @Override
    public String getApiConfigName() {
        return ApiConfigNameEnum.RESOURCE.getName();
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }

    @Override
    public VideoBaseRt<FeedsList> preProcess(SPIContext<ListBaseReqProperty, SearchResult> context) {
        SearchResult searchResult = context.getPreHandleResult().getData();
        if (searchResult == null) {
            //没有匹配的搜索结果
            return VideoBaseRt.successResponse(null);
        }
        if (CollectionUtils.isEmpty(searchResult.getResults())) {
            //没有匹配的搜索结果
            FeedsList feedsList = new FeedsList();
            feedsList.setHasMore(false);
            feedsList.setOffset(context.getRequest().getOffset());
            return VideoBaseRt.successResponse(feedsList);
        }
        return null;
    }

    @Override
    public Map<String, String> getParams(SPIContext<ListBaseReqProperty, SearchResult> context) {
        return null;
    }

    @Override
    public Object getRequestBody(SPIContext<ListBaseReqProperty, SearchResult> context) {
        SearchResult data = context.getPreHandleResult().getData();
        if ((data == null) || CollectionUtils.isEmpty(data.getResults())) {
            return null;
        }

        List<String> docIds = new LinkedList<>();
        for (SearchVideoInfo videoContent : data.getResults()) {
            if ((videoContent != null) && !StringUtils.isEmpty(videoContent.getId())) {
                docIds.add(videoContent.getId());
            }
        }
        ListBaseReqProperty request = context.getRequest();
        return getRequestMiddleParam(request.getAttributeValues(), docIds);
    }

    @Override
    public VideoBaseRt<FeedsList> handleResult(SPIContext<ListBaseReqProperty, SearchResult> context) {
        String response = context.getResponse();
        SearchResult preHandleResult = context.getPreHandleResult().getData();
        ResourceResult batchResource = StringUtils.isNotEmpty(response) ? JsonTools.toMap(response, ResourceResult.class)
                : null;
        if ((batchResource == null) || (batchResource.getCode() != 0)) {
            throw new InvalidDataRuntimeException(
                    (batchResource == null) ? THIRD_PARTY_API_RESP_EXCEPTION : Integer.toString(batchResource.getCode()));
        }
        ListBaseReqProperty request = context.getRequest();
        if ((batchResource.getData() == null) || CollectionUtils.isEmpty(batchResource.getData().getResourceList())) {
            FeedsList feedsList = new FeedsList();
            feedsList.setHasMore(request.getLimit().equals(preHandleResult.getSize()));
            feedsList.setOffset(request.getOffset() + preHandleResult.getSize());
            return VideoBaseRt.successResponse(feedsList);
        }

        List<VideoResource> resourceList = batchResource.getData().getResourceList();
        List<SearchVideoInfo> results = preHandleResult.getResults();
        Map<String, String> titleMap = new HashMap<>((int) (results.size() / 0.75 + 1));
        Map<String, String> authorMap = new HashMap<>((int) (results.size() / 0.75 + 1));

        for (SearchVideoInfo videoContent : results) {
            if ((videoContent != null) && !StringUtils.isEmpty(videoContent.getId())) {
                titleMap.put(videoContent.getId(), videoContent.getTitle());
                authorMap.put(videoContent.getId(), videoContent.getAuthor());
            }
        }
        ExtraInfo extraInfo = new ExtraInfo();
        extraInfo.setTaggedTitle(titleMap);
        extraInfo.setTaggedAuthor(authorMap);

        FeedsList feedsList = youliResourceService.getFromContentMiddle(context.getRequest(), resourceList, extraInfo);
        feedsList.setTransparent(preHandleResult.getTransparent());
        feedsList.setOffset(request.getOffset() + preHandleResult.getSize());
        feedsList.setHasMore(request.getLimit().equals(preHandleResult.getSize()));
        return VideoBaseRt.successResponse(feedsList);
    }

    private Map<String, Object> getRequestMiddleParam(AttributeValues attributeValues, List<String> idList) {
        Map<String, Object> params = new HashMap<>();
        if (attributeValues != null) {
            params.put("ip", attributeValues.getIp());
            params.put("network", attributeValues.getNetwork());
            if (attributeValues.getBuuid() != 0) {
                params.put("buuid", String.valueOf(attributeValues.getBuuid()));
            }
        }
        params.put("appId", "2");
        params.put("idList", idList);
        params.put("saveMode", "0");
        params.put("needRelation", Boolean.FALSE.toString());
        return params;
    }
}