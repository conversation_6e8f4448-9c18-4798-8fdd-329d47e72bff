package com.heytap.video.youli.spi.search;

import com.heytap.video.thirdparty.constant.ApiConfigNameEnum;
import com.heytap.video.thirdparty.spi.search.VideoSuggestionSPI;
import com.heytap.video.youli.model.search.SearchSuggestion;
import com.heytap.video.youli.model.search.SearchSuggestionResult;
import com.oppo.browser.common.app.lib.encryption.JsonTools;
import com.oppo.browser.video.common.pubobj.resource.VideoSearchSuggestionReqProperty;
import com.oppo.cpc.video.framework.lib.exception.InvalidDataRuntimeException;
import com.oppo.cpc.video.framework.lib.thirdparty.SPIContext;
import com.oppo.cpc.video.framework.lib.thirdparty.VideoBaseRt;

import static com.oppo.cpc.video.framework.lib.thirdparty.constant.OutCodeConstant.THIRD_PARTY_API_RESP_EXCEPTION;

import com.oppo.cpc.video.framework.lib.thirdparty.spi.AbstractYamlHttpSPI;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * youli源联想词搜索SPI实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class YouliVideoSuggestionSPI
        extends AbstractYamlHttpSPI<VideoSearchSuggestionReqProperty, ArrayList<String>, Serializable>
        implements VideoSuggestionSPI<ArrayList<String>, Serializable> {
    @Override
    public String getApiConfigName() {
        return ApiConfigNameEnum.SUGGESTION.getName();
    }

    @Override
    public Map<String, String> getParams(SPIContext<VideoSearchSuggestionReqProperty, Serializable> context) {
        VideoSearchSuggestionReqProperty request = context.getRequest();
        Map<String, String> params = new HashMap<>();
        params.put("keyword", request.getKeyword());
        params.put("version", String.valueOf(request.getVersion()));
        params.put("appId", request.getAppId());
        params.put("requestId", request.getRequestId());
        params.put("session", request.getSession());
        params.put("feedssession", request.getFeedssession());
        params.put("quickEngineVersion", String.valueOf(request.getQuickEngineVersion()));
        params.put("f", "json");
        return params;
    }

    @Override
    public VideoBaseRt<ArrayList<String>> handleResult(SPIContext<VideoSearchSuggestionReqProperty, Serializable> context) {
        String response = context.getResponse();
        SearchSuggestionResult searchSuggestionResult = StringUtils.isEmpty(response) ? null
                : JsonTools.toMap(response, SearchSuggestionResult.class);

        // 三方api响应码
        if ((searchSuggestionResult == null) || (searchSuggestionResult.getRet() != 0)) {
            log.warn("getSuggestionResult error, the request:{}, the result:{}", context.getRequest(), response);
            throw new InvalidDataRuntimeException(
                    (searchSuggestionResult == null) ? THIRD_PARTY_API_RESP_EXCEPTION : Integer.toString(searchSuggestionResult.getRet()));
        }

        List<SearchSuggestion> results = searchSuggestionResult.getResult();
        ArrayList<String> suggestionList = CollectionUtils.isEmpty(results) ? null
                : (ArrayList<String>) results.stream().map(SearchSuggestion::getTitle).collect(Collectors.toList());
        return VideoBaseRt.successResponse(suggestionList);
    }
}