package com.heytap.video.youli.spi.interact;

import com.heytap.video.thirdparty.constant.ApiConfigNameEnum;
import com.heytap.video.thirdparty.spi.interact.VideoLikeFavoriteSPI;
import com.heytap.video.youli.model.content.DynUpdateCount;
import com.heytap.video.youli.utils.SignUtil;
import com.oppo.browser.common.app.lib.utils.SysInfo;
import com.oppo.browser.video.common.pubobj.resource.FavoriteHistoryReqProperty;
import com.oppo.cpc.video.framework.lib.thirdparty.SPIContext;
import com.oppo.cpc.video.framework.lib.thirdparty.VideoBaseRt;
import com.oppo.cpc.video.framework.lib.thirdparty.spi.AbstractYamlHttpSPI;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * Created by xianyang.yxy on 2021/4/1 17:46.
 *
 * <AUTHOR>
 */
@Service
public class YouliVideoLikeFavoriteSPI extends AbstractYamlHttpSPI<FavoriteHistoryReqProperty, Boolean, Serializable> implements
        VideoLikeFavoriteSPI {

    @Override
    public String getApiConfigName() {
        return ApiConfigNameEnum.RESOURCE_UPDATE.getName();
    }

    @Override
    public Map<String, String> getParams(SPIContext<FavoriteHistoryReqProperty, Serializable> context) {
        return null;
    }

    @Override
    public Object getRequestBody(SPIContext<FavoriteHistoryReqProperty, Serializable> context) {
        FavoriteHistoryReqProperty request = context.getRequest();
        DynUpdateCount dynUpdateCount = new DynUpdateCount();
        dynUpdateCount.setLikeCount("-1".equals(request.getValue()) ? -1 : 1);
        dynUpdateCount.setResourceId(NumberUtils.toLong(request.getDocid()));

        List<DynUpdateCount> resourceDynList = Arrays.asList(dynUpdateCount);
        TreeMap<String, Object> dynUpdateParam = new TreeMap<>();
        dynUpdateParam.put("appId", 2);
        dynUpdateParam.put("resourceDynList", resourceDynList);
        dynUpdateParam.put("timestamp", SysInfo.getUnixTimeStamp());
        dynUpdateParam.put("sign", SignUtil.makeSignForResourceApi(dynUpdateParam));
        return dynUpdateParam;
    }

    @Override
    public VideoBaseRt<Boolean> handleResult(SPIContext<FavoriteHistoryReqProperty, Serializable> context) {
        return VideoBaseRt.successResponse(true);
    }
}
