package com.heytap.video.youli.spi.search;

import com.heytap.video.thirdparty.constant.ApiConfigNameEnum;
import com.heytap.video.thirdparty.spi.search.VideoSearchSPI;
import com.heytap.video.youli.model.search.SearchResult;
import com.oppo.browser.common.app.lib.encryption.JsonTools;
import com.oppo.browser.video.common.pubobj.resource.ListBaseReqProperty;
import com.oppo.cpc.video.framework.lib.exception.InvalidDataRuntimeException;
import com.oppo.cpc.video.framework.lib.thirdparty.SPIContext;
import com.oppo.cpc.video.framework.lib.thirdparty.VideoBaseRt;
import static com.oppo.cpc.video.framework.lib.thirdparty.constant.OutCodeConstant.THIRD_PARTY_API_RESP_EXCEPTION;
import com.oppo.cpc.video.framework.lib.thirdparty.spi.AbstractYamlHttpSPI;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * youli源视频搜索步骤1,根据关键词获取视频id
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class YouliVideoSearchSPIStep1 extends AbstractYamlHttpSPI<ListBaseReqProperty, SearchResult, Serializable>
        implements VideoSearchSPI<SearchResult, Serializable> {
    @Override
    public String getApiConfigName() {
        return ApiConfigNameEnum.SEARCH.getName();
    }

    @Override
    public Map<String, String> getParams(SPIContext<ListBaseReqProperty, Serializable> context) {
        ListBaseReqProperty request = context.getRequest();
        Map<String, String> params = new HashMap<>();
        params.put("keyword", request.getKeyword());
        params.put("offset", String.valueOf(request.getOffset()));
        params.put("number", String.valueOf(request.getLimit()));
        params.put("imei", String.valueOf(request.getAttributeValues().getImei()));
        return params;
    }

    @Override
    public VideoBaseRt<SearchResult> handleResult(SPIContext<ListBaseReqProperty, Serializable> context) {
        String response = context.getResponse();
        SearchResult searchResult = StringUtils.isEmpty(response) ? null : JsonTools.toMap(response, SearchResult.class);

        // 三方api响应码
        if ((searchResult == null) || (searchResult.getCode() != 0)) {
            log.warn("getVideoSearchSPI1Result error, the request:{}, the result:{}", context.getRequest(), response);
            throw new InvalidDataRuntimeException((searchResult == null) ? "third party null result" : searchResult.getMsg(),
                    (searchResult == null) ? THIRD_PARTY_API_RESP_EXCEPTION : Integer.toString(searchResult.getCode()));
        }

        return VideoBaseRt.successResponse(searchResult);
    }
}