package com.heytap.video.youli.spi.search;

import com.heytap.video.thirdparty.constant.ApiConfigNameEnum;
import com.heytap.video.thirdparty.spi.search.VideoHotKeywordsSPI;
import com.heytap.video.youli.model.search.HotKeywordResult;
import com.heytap.video.youli.model.search.HotkeywordModel;
import com.oppo.browser.common.app.lib.encryption.JsonTools;
import com.oppo.browser.video.common.pubobj.resource.VideoHotKeywordsReqProperty;
import com.oppo.cpc.video.framework.lib.exception.InvalidDataRuntimeException;
import com.oppo.cpc.video.framework.lib.thirdparty.SPIContext;
import com.oppo.cpc.video.framework.lib.thirdparty.VideoBaseRt;
import static com.oppo.cpc.video.framework.lib.thirdparty.constant.OutCodeConstant.THIRD_PARTY_API_RESP_EXCEPTION;
import com.oppo.cpc.video.framework.lib.thirdparty.spi.AbstractYamlHttpSPI;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 自有源视频热词SPI实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class YouliVideoHotKeywordsSPI extends AbstractYamlHttpSPI<VideoHotKeywordsReqProperty, ArrayList<String>, Serializable>
        implements VideoHotKeywordsSPI<ArrayList<String>, Serializable> {
    @Override
    public String getApiConfigName() {
        return ApiConfigNameEnum.HOT_KEY_WORDS.getName();
    }

    @Override
    public Map<String, String> getParams(SPIContext<VideoHotKeywordsReqProperty, Serializable> context) {
        Map<String, String> params = new HashMap<>();
        params.put("ctype", "video");
        return params;
    }

    @Override
    public VideoBaseRt<ArrayList<String>> handleResult(SPIContext<VideoHotKeywordsReqProperty, Serializable> context) {
        String response = context.getResponse();
        HotKeywordResult hotKeywordResult = StringUtils.isEmpty(response) ? null
                : JsonTools.toMap(response, HotKeywordResult.class);

        // 三方api响应码
        if ((hotKeywordResult == null) || (hotKeywordResult.getCode() != 0)) {
            log.warn("getHotKeywordsResult error, the request:{}, the result:{}", context.getRequest(), response);
            throw new InvalidDataRuntimeException(
                    (hotKeywordResult == null) ? THIRD_PARTY_API_RESP_EXCEPTION : Integer.toString(hotKeywordResult.getCode()));
        }

        ArrayList<String> keywords = CollectionUtils.isEmpty(hotKeywordResult.getResults()) ? null :
            (ArrayList<String>) hotKeywordResult.getResults().stream().map(HotkeywordModel::getKeyword).collect(Collectors.toList());
        return VideoBaseRt.successResponse(keywords);
    }
}
