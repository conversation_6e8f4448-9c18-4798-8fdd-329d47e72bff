package com.heytap.video.youli.spi.common;

import com.heytap.video.thirdparty.constant.ApiConfigNameEnum;
import com.oppo.browser.common.app.lib.encryption.JsonTools;
import com.oppo.browser.video.common.pubobj.FeedsConstant;
import com.oppo.browser.video.common.pubobj.resource.FeedsListReqProperty;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.Article;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.FeedsList;
import com.oppo.cpc.video.framework.lib.thirdparty.SPIContext;
import com.oppo.cpc.video.framework.lib.thirdparty.VideoBaseRt;
import com.oppo.cpc.video.framework.lib.thirdparty.spi.AbstractYamlHttpSPI;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import java.util.List;
import java.util.Map;

/**
 * 获取视频评论数SPI
 *
 * <AUTHOR>
 */
@Service
public class YouliVideoCommentCountSPI extends AbstractYamlHttpSPI<FeedsListReqProperty, FeedsList, FeedsList> {

    @Override
    public String getApiConfigName() {
        return ApiConfigNameEnum.COMMENT_COUNT.getName();
    }

    @Override
    public VideoBaseRt<FeedsList> preProcess(SPIContext<FeedsListReqProperty, FeedsList> context) {
        boolean commentSwitch = Boolean.TRUE.equals(getExtendsConfig("commentSwitch"));
        VideoBaseRt<FeedsList> preHandleResult = context.getPreHandleResult();
        if (!commentSwitch || preHandleResult.getData() == null || CollectionUtils
            .isEmpty(preHandleResult.getData().getArticles())) {
            //不调用评论数接口
            return preHandleResult;
        }
        return null;
    }

    @Override
    public Map<String, String> getParams(SPIContext<FeedsListReqProperty, FeedsList> context) {
        return null;
    }


    @Override
    public Object getRequestBody(SPIContext<FeedsListReqProperty, FeedsList> context) {
        FeedsList feedsList = context.getPreHandleResult().getData();
        StringBuilder sb = new StringBuilder();
        for (Article article : feedsList.getArticles()) {
            sb.append(article.getId()).append(",");
        }
        String docIdsParam = sb.deleteCharAt(sb.lastIndexOf(",")).toString();
        Map<String, String> params = new HashedMap<>();
        params.put("businessType", "VIDEO");
        params.put("docIds", docIdsParam);
        params.put("docId", "fake");
        params.put("source", FeedsConstant.TopResource.YOULI);
        return JsonTools.toJsonString(params);
    }

    @Override
    public VideoBaseRt<FeedsList> handleResult(SPIContext<FeedsListReqProperty, FeedsList> context) {
        String response = context.getResponse();
        VideoBaseRt<FeedsList> preHandleResult = context.getPreHandleResult();
        if (StringUtils.isEmpty(response)) {
            return preHandleResult;
        }
        Map<String, Object> responseMap = JsonTools.toMap(response, Map.class);
        Integer rt = (Integer) responseMap.get("code");
        Map<String, Integer> commentCount = (Map<String, Integer>) responseMap.get("data");

        if (rt != null && rt == 0) {
            //设置评论数
            setArticlesCommentCount(preHandleResult.getData().getArticles(), commentCount);
        }
        return preHandleResult;
    }

    //@Override
    public VideoBaseRt<FeedsList> fallbackData(SPIContext<FeedsListReqProperty, FeedsList> context) {
        //获取评论数据异常时将评论数降级为0
        return context.getPreHandleResult();
    }

    private void setArticlesCommentCount(@Nonnull List<Article> articles, Map<String, Integer> commentCount) {
        if (MapUtils.isEmpty(commentCount)) {
            return;
        }

        Integer cmtCount;
        for (Article article : articles) {
            cmtCount = commentCount.get(article.getId());
            if (cmtCount != null) {
                article.setCmtCnt(cmtCount);
            }
        }
    }

    @Override
    public String getServiceType() {
        //适用于所有类型
        return null;
    }
}