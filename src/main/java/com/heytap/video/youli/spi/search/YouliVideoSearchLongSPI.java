package com.heytap.video.youli.spi.search;

import com.alibaba.fastjson.JSON;
import com.heytap.video.client.entity.drawitem.LvDrawerItemVO;
import com.heytap.video.thirdparty.constant.ApiConfigNameEnum;
import com.heytap.video.thirdparty.spi.search.VideoSearchLongSPI;
import com.heytap.video.youli.config.YouliApiConfig;
import com.heytap.video.youli.model.search.LongSearchHttpResponse;
import com.heytap.video.youli.model.search.LongSearchItem;
import com.oppo.browser.common.app.lib.encryption.JsonTools;
import com.oppo.browser.video.common.pubobj.constant.FeedsItemEnum;
import com.oppo.browser.video.common.pubobj.resource.ListBaseReqProperty;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.*;
import com.oppo.browser.video.common.pubobj.resultObj.longvideo.LongContentSource;
import com.oppo.cpc.video.framework.lib.exception.InvalidDataRuntimeException;
import com.oppo.cpc.video.framework.lib.thirdparty.SPIContext;
import com.oppo.cpc.video.framework.lib.thirdparty.VideoBaseRt;

import static com.oppo.cpc.video.framework.lib.thirdparty.constant.OutCodeConstant.THIRD_PARTY_API_RESP_EXCEPTION;

import com.oppo.cpc.video.framework.lib.thirdparty.spi.AbstractYamlHttpSPI;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * youli源长视频搜索,根据关键词获取长视频
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class YouliVideoSearchLongSPI extends AbstractYamlHttpSPI<ListBaseReqProperty, FeedsList, Serializable>
        implements VideoSearchLongSPI<FeedsList, Serializable> {
    @Autowired
    private YouliApiConfig youliApiConfig;

    @Override
    public String getApiConfigName() {
        return ApiConfigNameEnum.SEARCHLONG.getName();
    }

    @Override
    public Map<String, String> getParams(SPIContext<ListBaseReqProperty, Serializable> context) {
        ListBaseReqProperty request = context.getRequest();
        Map<String, String> params = new HashMap<>();
        if (request.getVersion() < 52300) {
            int longNum = Math.min(NumberUtils.toInt(request.getAttachments().get("longNum"), 1), 10);
            request.setLimit(longNum);
            request.setPage(1);
        } else if (request.getSearchType() == 1 && request.getPage() == 1) {
            request.setLimit(youliApiConfig.getAllSearchLongNum());
        }
        params.put("keyword", request.getKeyword());
        params.put("searchType", String.valueOf(request.getSearchType()));
        params.put("pageIndex", String.valueOf(request.getPage()));
        params.put("pageSize", String.valueOf(request.getLimit()));
        params.put("vipType", request.getVipType());
        params.put("version", String.valueOf(request.getVersion()));
        params.put("appId", request.getAppId());
        params.put("requestId", request.getRequestId());
        params.put("session", request.getSession());
        params.put("feedssession", request.getFeedssession());
        params.put("deviceType", getDeviceType(request));
        params.put("dv", request.getAttributeValues().getPhone());
        params.put("buuid", String.valueOf(request.getAttributeValues().getBuuid()));
        params.put("f", "json");
        params.put("quickEngineVersion", String.valueOf(request.getQuickEngineVersion()));
        params.put("lastSearchTab", request.getLastSearchTab());
        return params;
    }

    public String getDeviceType(ListBaseReqProperty request) {
        try {
            Integer type = request.getAttributeValues().getDeviceType();
            if (type == null || type == 5) {
                return "0";
            }
            return String.valueOf(type);
        } catch (Exception e) {
            log.error("getDeviceType error", e);
            return "0";
        }
    }

    @Override
    public VideoBaseRt<FeedsList> handleResult(SPIContext<ListBaseReqProperty, Serializable> context) {
        String response = context.getResponse();
        LongSearchHttpResponse longSearch = StringUtils.isEmpty(response) ? null
                : JSON.parseObject(context.getResponse(), LongSearchHttpResponse.class);

        // 三方api响应码
        if ((longSearch == null) || (longSearch.getRet() != 0) || (longSearch.getResult() == null)) {
            log.warn("getLongSearchResult error, the request:{}, the result:{}", context.getRequest(), response);
            throw new InvalidDataRuntimeException("getLongSearchResult error", (longSearch == null) ? THIRD_PARTY_API_RESP_EXCEPTION : Integer.toString(longSearch.getRet()));
        }
        FeedsList feedsList = new FeedsList();
        ListBaseReqProperty request = context.getRequest();
        feedsList.setOffset(request.getOffset() + longSearch.getResult().getPageSize());
        feedsList.setHasMore(longSearch.getResult().getHasMore() > 0);
        feedsList.setSearchTab(longSearch.getResult().getSearchTab());

        if (CollectionUtils.isNotEmpty(longSearch.getResult().getLongVideoSearchResult())) {
            for (LongVideo longVideo : longSearch.getResult().getLongVideoSearchResult()) {
                searchItemToLongVideo(longVideo);
                Item item = new Item();
                item.setId(longVideo.getSid());
                item.setMap(FeedsItemEnum.LONGVIDEO.getMap());
                item.setType(FeedsItemEnum.LONGVIDEO.getType());
                feedsList.addLongVideo(longVideo);
                feedsList.addItem(item);
            }
        }
        LongVideoRecommend longVideoRecommend = longSearch.getResult().getLongVideoRecommend();
        if (longVideoRecommend != null && CollectionUtils.isNotEmpty(longVideoRecommend.getContents())) {
            for (LongVideo longVideo : longVideoRecommend.getContents()) {
                searchItemToLongVideo(longVideo);
            }
            feedsList.setLongVideoRecommend(longVideoRecommend);
        }

        LongVideoSeries longVideoSeries = longSearch.getResult().getLongVideoSeries();
        if (longVideoSeries != null && CollectionUtils.isNotEmpty(longVideoSeries.getContents())) {
            for (LongVideo longVideo : longVideoSeries.getContents()) {
                searchItemToLongVideo(longVideo);
            }
            feedsList.setLongVideoSeries(longVideoSeries);
        }

        LongVideoTag longVideoTag = longSearch.getResult().getLongVideoTag();
        if (longVideoTag != null && CollectionUtils.isNotEmpty(longVideoTag.getContents())) {
            for (LongVideo longVideo : longVideoTag.getContents()) {
                searchItemToLongVideo(longVideo);
            }
            feedsList.setLongVideoTag(longVideoTag);
        }

        if (CollectionUtils.isNotEmpty(longSearch.getResult().getLongVideoBannerList())) {
            List<LongVideo> longVideoBannerList = new ArrayList<>();
            for (LvDrawerItemVO lvDrawerItemVO : longSearch.getResult().getLongVideoBannerList()) {
                LongVideo longVideo = new LongVideo();
                longVideo.setContentType("banner");
                List<String> imageList = new ArrayList<>();
                imageList.add(lvDrawerItemVO.getImgUrl());
                longVideo.setHorizontalIcon(imageList);
                longVideo.setDeepLink(lvDrawerItemVO.getDeepLink());
                longVideoBannerList.add(longVideo);
            }
            feedsList.setLongVideoBannerList(longVideoBannerList);
        }

        LongVideoInterveneCard longVideoDefaultRecommend = longSearch.getResult().getLongVideoDefaultRecommend();
        if (longVideoDefaultRecommend != null && CollectionUtils.isNotEmpty(longVideoDefaultRecommend.getContents())) {
            for (LongVideo longVideo : longVideoDefaultRecommend.getContents()) {
                searchItemToLongVideo(longVideo);
            }
            feedsList.setLongVideoDefaultRecommend(longVideoDefaultRecommend);
        }

        LongVideoActor longVideoActor = longSearch.getResult().getLongVideoActor();
        if (longVideoActor != null && CollectionUtils.isNotEmpty(longVideoActor.getContents())) {
            for (LongVideo longVideo : longVideoActor.getContents()) {
                searchItemToLongVideo(longVideo);
            }
            feedsList.setLongVideoActor(longVideoActor);
        }

        return VideoBaseRt.successResponse(feedsList);
    }


    private void searchItemToLongVideo(LongVideo longVideo) {
        longVideo.setId(longVideo.getSid());
        longVideo.setAlgSource("longvideo");
        if (CollectionUtils.isNotEmpty(longVideo.getMultipleSourceCode())) {
            longVideo.setSourceList(longVideo.getMultipleSourceCode().stream().filter(s -> StringUtils.isNotEmpty(s))
                    .map(s -> new LongContentSource(s)).collect(Collectors.toList()));
        }
    }
}