package com.heytap.video.youli.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

@Setter
@Getter
@ToString
public class ExtraInfo {
    private String rid; //推荐时rid
    private String bidlst; //推荐时bidlst
    private Map<String, String> taggedTitle; //搜索时带标记的标题文案
    private Map<String, String> taggedAuthor; //搜索时带标记的作者文案
    private String dpOpenFrom; //构建deepLinkUrl返回值时拼接的openFrom值

}
