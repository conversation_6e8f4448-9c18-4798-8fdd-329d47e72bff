package com.heytap.video.youli.model.search;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class LongSearchItem {
    private String sid; //标准化剧id
    private String title; //剧标题
    private String contentType; //一级分类：（movie：电影，tv：电视剧，show：综艺，doc：纪录片，comic：动漫，kids：少儿，live：直播）
    private String copyrightCode; //版权方
    private String verticalIcon; //竖图
    private String horizontalIcon; //横图
    private String programInfo; //更换新信息
    private String area; //地区
    private String year; //年份
    private double sourceScore; //评分
    private String directors; //导演
    private String stars; //演员
    private String languages; //语言
    private String description;
    private double releScore; //搜索相关度评分
    private int payStatus; //付费类型
    private String markCode; //角标
    private String source; //
    private String tags; //标签
    private int featureType; //目前固定返回1：正片
    private List<String> multipleSourceCode; //来源（逗号隔开）
    private String webUrl;
    private String deepLink;
    private String recommendInfo;
    private String markCodeUrl;
}
