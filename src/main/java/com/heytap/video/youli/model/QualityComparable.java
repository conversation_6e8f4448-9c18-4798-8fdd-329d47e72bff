package com.heytap.video.youli.model;

import com.heytap.video.youli.model.content.VideoInfo;
import java.io.Serializable;
import java.util.Comparator;

public class QualityComparable implements Comparator<VideoInfo>, Serializable {
    private static final long serialVersionUID = -1L;

    private static QualityComparable instance = new QualityComparable();

    private QualityComparable() {
    }

    public static QualityComparable getInstance() {
        return instance;
    }

    @Override
    public int compare(VideoInfo o1, VideoInfo o2) {
        return o1.getQuality() - o2.getQuality();
    }
}