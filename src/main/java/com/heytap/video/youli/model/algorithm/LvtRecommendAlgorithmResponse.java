package com.heytap.video.youli.model.algorithm;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/12 17:22
 */

public class LvtRecommendAlgorithmResponse implements Serializable {
    private static final long serialVersionUID = 1;
    /**
     * 状态标识 0正常
     */
    private Integer status;
    /**
     * 推荐数据
     */
    private List<LvtRecommendAlgorithmData> data;
    /**
     * 透传字段
     */
    private String transparent;
    /**
     * 实际推荐物料数
     */
    private Integer num;
    /**
     * 用于链路追踪
     */
    private String traceId;

    private Object ext;

    /**
     * 是否为兜底缓存数据
     */
    private boolean fallback = false;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<LvtRecommendAlgorithmData> getData() {
        return data;
    }

    public void setData(List<LvtRecommendAlgorithmData> data) {
        this.data = data;
    }

    public String getTransparent() {
        return transparent;
    }

    public void setTransparent(String transparent) {
        this.transparent = transparent;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public Object getExt() {
        return ext;
    }

    public void setExt(Object ext) {
        this.ext = ext;
    }

    public boolean isFallback() {
        return fallback;
    }

    public void setFallback(boolean fallback) {
        this.fallback = fallback;
    }
}