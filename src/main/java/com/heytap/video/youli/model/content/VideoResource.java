package com.heytap.video.youli.model.content;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class VideoResource {
    private String artificialTitle; //人工标题
    private List<ArtificialCoverImage> artificialViewPics; //人工封面
    private Author author;
    private String[] category;
//    private String commentPageUrl;
    private int contentType;
    private VideoCountInfo count;
    private List<CoverImage> coverList;
//    private List<TailoredCoverImage> tailoredCoverList;

    @JsonProperty("id")
    @JsonAlias("resourceId")
    private String id;

    private int publishTime;
    private int styleType;
    private String summary;
    private String thirdPartyId;
    private String title;
    private String url;
    private String vendor;

//    private VideoDetailInfo videoDetail;
    private VideoDetailInfo resourceDetail;

//    private List<FilterWord> filterWordList;

    private long updatetime;
    private List<RelationData> relationInfoList;
    private List<CoverImage> smartPicList;
}
