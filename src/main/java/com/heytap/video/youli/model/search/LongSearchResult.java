package com.heytap.video.youli.model.search;

import com.heytap.video.client.entity.drawitem.LvDrawerItemVO;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class LongSearchResult {
    private List<LongVideo> longVideoSearchResult;
    private LongVideoRecommend longVideoRecommend;
    private LongVideoSeries longVideoSeries;
    private LongVideoTag longVideoTag;
    private List<LvDrawerItemVO> longVideoBannerList;
    private LongVideoInterveneCard longVideoDefaultRecommend;
    private LongVideoActor longVideoActor;
    private String searchTab;

    private int hasMore; //是否有下一页（0否，1是）
    private int pageIndex;
    private int pageSize;
}
