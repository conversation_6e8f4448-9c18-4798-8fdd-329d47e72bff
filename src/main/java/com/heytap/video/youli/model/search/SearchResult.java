package com.heytap.video.youli.model.search;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@ToString
public class SearchResult implements Serializable {

    private static final long serialVersionUID = -1L;

    private int code;

    private String msg;

    private int size;

    @JsonProperty("search_id")
    private String searchId;

    private String transparent;

    private List<SearchVideoInfo> results;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public String getSearchId() {
        return searchId;
    }

    public void setSearchId(String searchId) {
        this.searchId = searchId;
    }

    public String getTransparent() {
        return transparent;
    }

    public void setTransparent(String transparent) {
        this.transparent = transparent;
    }

    public List<SearchVideoInfo> getResults() {
        return results;
    }

    public void setResults(List<SearchVideoInfo> results) {
        this.results = results;
    }
}