package com.heytap.video.youli.model.content;

import lombok.Data;

import java.io.Serializable;

/**
 * 长视频关联对象
 */
@Data
public class Relation implements Serializable {
    private static final long serialVersionUID = -1723400058754182926L;

    /**
     * 剧头id
     */
    private String sid;

    /**
     * 剧集id
     */
    private String eid;

    /**
     * 标题
     */
    private String title;

    /**
     * 正片一级分类(电影/影视剧/动漫)
     */
    private String chineseType;

    /**
     * 正片标签1：地区(内地剧/香港)
     */
    private String area;

    /**
     * 正片标签2：题材(古装/剧情)
     */
    private String category;

    /**
     * 正片标签3：年份(2020)
     */
    private String year;

    /**
     * 内容来源：(搜狐/腾讯)
     */
    private String source;

    /**
     * 剧头封面(横图)
     */
    private String horizontalImage;

    /**
     * 跳转链接
     */
    private String deepLinkUrl;
}
