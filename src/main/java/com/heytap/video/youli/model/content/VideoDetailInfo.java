package com.heytap.video.youli.model.content;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Setter
@Getter
@ToString
public class VideoDetailInfo implements Serializable {
    private static final long serialVersionUID = -1L;
    private String desc;
    private double duration;

// private List<VideoInfo> videoList;

    @JsonProperty("resourceDetailList")
    @JsonAlias("resouceDetailList")
    private List<VideoInfo> resourceDetailList;

}
