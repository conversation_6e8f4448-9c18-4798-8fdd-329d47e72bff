package com.heytap.video.youli.model.algorithm;

import java.util.List;
import lombok.ToString;

@ToString
public class RecommendList{

    private long code = -1;

    private String message;

    private String rid;

    private List<Recommend> result;

    public List<Recommend> getResult() {
        return result;
    }

    public void setResult(List<Recommend> result) {
        this.result = result;
    }

    public long getCode() {
        return code;
    }

    public void setCode(long code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }

}