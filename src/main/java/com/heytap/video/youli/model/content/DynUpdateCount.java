package com.heytap.video.youli.model.content;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * Description: 点赞、播放数等数据同步到中台时的具体数据
 * User: liangjinlin
 * Date: 2019-10-28
 */
@ToString
@Getter
@Setter
public class DynUpdateCount {

    private long resourceId; //资源Id

    private byte coverType = 2; //入库形式：1是覆盖，2是增量

    private long clickCount; //点击

    private long commentCount; //评论

    private long likeCount; //点赞

    private long dislikeCount; //不喜欢

    private long playCount; //播放

    private long shareCount; //分享

    private long viewCount; //曝光
}