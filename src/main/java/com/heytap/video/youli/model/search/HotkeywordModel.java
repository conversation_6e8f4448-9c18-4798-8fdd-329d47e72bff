package com.heytap.video.youli.model.search;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @description:
 * @author: hanhao
 * @time: 2020/8/27 17:08
 */
@Data
public class HotkeywordModel implements Serializable {
    private static final long serialVersionUID = -2736461321586826223L;
    private String keyword;
    private int heat;
    private int growth;
    private String source;
    @JsonProperty("pub_time")
    private String pubTime;
    @JsonProperty("rank_score")
    private float rankScore;
    private String transparent;
}
