package com.heytap.video.youli.model.algorithm;

import com.heytap.video.youli.model.content.FilterWord;
import com.heytap.video.youli.model.content.VideoResource;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString(callSuper = true)
public class Recommend extends VideoResource {

//    private String transparent;

    private List<FilterWord> dislikeTags;

    private List<AlgoRelationInfo> algoRelationInfoList; //算法下发的视频关联内容，如游戏

//    private String label;

//    private int videoType = 2; //2中台短视频，4爆赞小视频

}