package com.heytap.video.youli.mapstruct;

import com.heytap.video.youli.model.algorithm.AlgorithmCacheData;
import com.heytap.video.youli.model.algorithm.LvtRecommendAlgorithmData;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AlgorithmDataConvert {

    AlgorithmDataConvert INSTANCE = Mappers.getMapper(AlgorithmDataConvert.class);

    AlgorithmCacheData toCacheData(LvtRecommendAlgorithmData src);

    LvtRecommendAlgorithmData toAlgorithmData(AlgorithmCacheData src);

}
