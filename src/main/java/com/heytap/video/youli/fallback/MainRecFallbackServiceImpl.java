package com.heytap.video.youli.fallback;

import com.heytap.video.fallback.service.FallbackCustomerService;
import com.heytap.video.youli.model.algorithm.RecommendList;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

@Service
public class MainRecFallbackServiceImpl implements FallbackCustomerService<RecommendList> {

    @Override
    public boolean responseDataValid(RecommendList recommendList) {
        return recommendList != null && recommendList.getCode() == 0 && CollectionUtils.isNotEmpty(recommendList.getResult());
    }

    @Override
    public String interfaceName() {
        return "mainRec";
    }
}
