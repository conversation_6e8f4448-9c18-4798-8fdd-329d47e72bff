package com.heytap.video.youli.service.executors;

import com.heytap.video.youli.biz.YouliResourceService;
import com.oppo.browser.common.next.executor.AbstractNextBackendExecutor;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import com.oppo.browser.video.common.pubobj.constant.RetConstant;
import com.oppo.browser.video.common.pubobj.resource.VideoInfoReqProperty;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.Article;
import com.oppo.browser.video.common.pubobj.resultObj.news.VideoArticleRt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 获取单个视频详情
 * 
 * <AUTHOR>
 *
 */
@Component("getVideoInfoBackendExecutor")
public class GetVideoInfoBackendExecutor extends AbstractNextBackendExecutor<VideoInfoReqProperty, VideoArticleRt> {
    @Autowired
    private YouliResourceService youliResourceService;

    @Override
    protected VideoArticleRt myExecute(VideoInfoReqProperty nRequest) {
        VideoArticleRt videoArticleRt = new VideoArticleRt();
        videoArticleRt.setRet(0);
        videoArticleRt.setMsg("success");
        try{
            Article article = youliResourceService.getArticleInfo(nRequest);
            videoArticleRt.setData(article);
        }catch(BizException e){
            videoArticleRt.setRet(e.getCode());
            videoArticleRt.setMsg(e.getMessage());
        }
        return videoArticleRt;
    }

    @Override
    protected VideoArticleRt buildBizExceptionResponse(VideoInfoReqProperty nRequest, BizException e) {
        return null;
    }

    @Override
    protected VideoArticleRt buildExceptionResponse(VideoInfoReqProperty nRequest, Exception e) {
        VideoArticleRt videoArticleRt = new VideoArticleRt();
        videoArticleRt.setRet(RetConstant.BAD_REQUEST);
        videoArticleRt.setMsg(e.getMessage());
        return videoArticleRt;
    }
}