package com.heytap.video.youli.service.executors;

import com.oppo.browser.common.pubobj.feeds.runtimeexceptions.BizRuntimeException;
import com.oppo.browser.video.common.pubobj.resource.BatchArticleInfoReqProperty;
import com.oppo.browser.video.common.pubobj.resultObj.news.VideoArticleMapRt;
import com.heytap.video.youli.biz.YouliResourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

@Component("getBatchVideoInfoBackendExecutor")
@Slf4j
public class GetBatchVideoInfoBackendExecutor {

    @Autowired
    private YouliResourceService youliResourceService;

    public CompletableFuture<VideoArticleMapRt> execute(BatchArticleInfoReqProperty nRequest) {
        VideoArticleMapRt videoArticleMapRt = new VideoArticleMapRt();
        videoArticleMapRt.setRet(0);
        videoArticleMapRt.setMsg("success");
        try{
            return youliResourceService.getBatchArticleInfo(nRequest).handle((mapArticle, throwable) -> {
                if(throwable == null){
                    videoArticleMapRt.setData(mapArticle);
                    return videoArticleMapRt;
                }
                Throwable wrappedThrowable = throwable.getCause();
                if(wrappedThrowable instanceof BizRuntimeException){
                    videoArticleMapRt.setRet(((BizRuntimeException)wrappedThrowable).getCode());
                    videoArticleMapRt.setMsg(wrappedThrowable.getMessage());
                }else{
                    log.error("getFeedsList error: ", throwable);
                    videoArticleMapRt.setRet(1400);
                    videoArticleMapRt.setMsg(throwable.getMessage());
                }
                return videoArticleMapRt;
            });
        }catch(Exception e){
            videoArticleMapRt.setRet(1400);
            videoArticleMapRt.setMsg("server exception:" + e.getMessage());
        }
        return CompletableFuture.completedFuture(videoArticleMapRt);
    }

}
