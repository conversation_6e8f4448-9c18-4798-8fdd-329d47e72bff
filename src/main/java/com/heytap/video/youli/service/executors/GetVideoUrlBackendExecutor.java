//package com.heytap.video.youli.service.executors;
//
//import com.oppo.browser.app.framework.utils.AppCommonUtils;
//import com.oppo.browser.common.app.lib.utils.SysInfo;
//import com.oppo.browser.common.next.executor.NextBackendExecutor;
//import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
//import com.oppo.browser.video.common.pubobj.constant.RetConstant;
//import com.oppo.browser.video.common.pubobj.resource.VideoURL302ReqProperty;
//import com.oppo.browser.video.common.pubobj.resultObj.news.VideoStringRt;
//import com.heytap.video.youli.config.VideoCDNConfig;
//import org.springframework.stereotype.Component;
//
///**
// * 302跳转获取实际播放地址
// *
// * <AUTHOR>
// *
// */
//@Component("getVideoUrlBackendExecutor")
//public class GetVideoUrlBackendExecutor extends NextBackendExecutor<VideoURL302ReqProperty, VideoStringRt> {
//    @Override
//    protected VideoStringRt myExecute(VideoURL302ReqProperty nRequest) throws BizException {
//        String timestamp = Integer.toHexString(SysInfo.getUnixTimeStamp()).toUpperCase();
//        String signature = AppCommonUtils.getmd5(VideoCDNConfig.getPrivateKey() + nRequest.getKeyword() + timestamp);
//        String url = VideoCDNConfig.getVideoUrl() + nRequest.getKeyword() + "?signature=" + signature + "&timestamp="
//                + timestamp;
//
//        VideoStringRt videoStringRt = new VideoStringRt();
//        videoStringRt.setData(url);
//        videoStringRt.setRet(0);
//        return videoStringRt;
//    }
//
//    @Override
//    protected VideoStringRt buildBizExceptionResponse(VideoURL302ReqProperty nRequest, BizException e) {
//        VideoStringRt videoStringRt = new VideoStringRt();
//        videoStringRt.setMsg(e.getMessage());
//        videoStringRt.setRet(e.getCode());
//        return videoStringRt;
//    }
//
//    @Override
//    protected VideoStringRt buildExceptionResponse(VideoURL302ReqProperty nRequest, Exception e) {
//        VideoStringRt videoStringRt = new VideoStringRt();
//        videoStringRt.setData(e.getMessage());
//        videoStringRt.setRet(RetConstant.BAD_REQUEST);
//        return videoStringRt;
//    }
//}