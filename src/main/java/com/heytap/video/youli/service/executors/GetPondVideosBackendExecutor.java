package com.heytap.video.youli.service.executors;

import com.oppo.browser.common.pubobj.feeds.runtimeexceptions.BizRuntimeException;
import com.oppo.browser.video.common.pubobj.resource.GetPondVideosReqProperty;
import com.oppo.browser.video.common.pubobj.resultObj.news.VideoFeedsListRt;
import com.heytap.video.youli.biz.YouliResourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

@Slf4j
@Component("getPondVideosBackendExecutor")
public class GetPondVideosBackendExecutor {

    @Autowired
    private YouliResourceService youliResourceService;

    public CompletableFuture<VideoFeedsListRt> execute(GetPondVideosReqProperty nRequest) {
        VideoFeedsListRt result;
        try {
            return youliResourceService.getPondVideos(nRequest).handle((videoFeedsListRt, throwable) -> {
                if(throwable == null){
                    return videoFeedsListRt;
                }
                VideoFeedsListRt rt = new VideoFeedsListRt();
                rt.setMsg(throwable.getMessage());
                if(throwable.getCause() instanceof BizRuntimeException){
                    rt.setRet(((BizRuntimeException)throwable.getCause()).getCode());
                }else{
                    log.error("getFeedsList error: ", throwable);
                    rt.setRet(1400);
                }
                return rt;
            });
        }catch(Exception e){
            log.error("youliResourceService.getPondVideos error:{}", nRequest, e);
            result = new VideoFeedsListRt();
            result.setRet(1400);
            result.setMsg(e.getMessage());
            return CompletableFuture.completedFuture(result);
        }
    }

}
