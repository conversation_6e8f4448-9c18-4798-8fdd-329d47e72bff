package com.heytap.video.youli.service.impl;

import com.heytap.video.youli.biz.contentmiddle.MiddleMediaResource;
import com.heytap.video.youli.config.YouliApiConfig;
import com.heytap.video.youli.metric.MetricConstant;
import com.heytap.video.youli.service.executors.GetBatchVideoInfoBackendExecutor;
import com.heytap.video.youli.service.executors.GetMediaInfoBackendExecutor;
import com.heytap.video.youli.service.executors.GetMediaVideoListBackendExecutor;
import com.heytap.video.youli.service.executors.GetRecVideoBackendExecutor;
import com.heytap.video.youli.service.executors.GetVideoInfoBackendExecutor;
import com.heytap.video.youli.service.executors.GetVideoListBackendExecutor;
import com.oppo.browser.common.pubobj.feeds.runtimeexceptions.BizRuntimeException;
import com.oppo.browser.video.common.next.executor.NextResponseEntity;
import com.oppo.browser.video.common.pubobj.resource.BatchArticleInfoReqProperty;
import com.oppo.browser.video.common.pubobj.resource.BatchVideoMediaInfoReqProperty;
import com.oppo.browser.video.common.pubobj.resource.FeedsAccuseReqProperty;
import com.oppo.browser.video.common.pubobj.resource.FeedsDislikeReqProperty;
import com.oppo.browser.video.common.pubobj.resource.FeedsListReqProperty;
import com.oppo.browser.video.common.pubobj.resource.VideoCommentAddReqProperty;
import com.oppo.browser.video.common.pubobj.resource.VideoCommentLikeReqProperty;
import com.oppo.browser.video.common.pubobj.resource.VideoCommentListReqProperty;
import com.oppo.browser.video.common.pubobj.resource.VideoCommentReqProperty;
import com.oppo.browser.video.common.pubobj.resource.VideoInfoReqProperty;
import com.oppo.browser.video.common.pubobj.resource.VideoMediaInfoReqProperty;
import com.oppo.browser.video.common.pubobj.resource.VideoMediaListReqProperty;
import com.oppo.browser.video.common.pubobj.resource.VideoQuickCommentReqProperty;
import com.oppo.browser.video.common.pubobj.resource.VideoRecListReqProperty;
import com.oppo.browser.video.common.pubobj.resource.VideoURL302ReqProperty;
import com.oppo.browser.video.common.pubobj.resource.VideoUpdateStatusReqProperty;
import com.oppo.browser.video.common.pubobj.resultObj.comment.VideoComment;
import com.oppo.browser.video.common.pubobj.resultObj.comment.VideoCommentList;
import com.oppo.browser.video.common.pubobj.resultObj.comment.VideoLikeComment;
import com.oppo.browser.video.common.pubobj.resultObj.comment.VideoNativeCommentListRt;
import com.oppo.browser.video.common.pubobj.resultObj.comment.VideoQuickCommentList;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.VideoMediaInfo;
import com.oppo.browser.video.common.pubobj.resultObj.media.VideoMediaInfoRt;
import com.oppo.browser.video.common.pubobj.resultObj.news.VideoArticleMapRt;
import com.oppo.browser.video.common.pubobj.resultObj.news.VideoArticleRt;
import com.oppo.browser.video.common.pubobj.resultObj.news.VideoBooleanRt;
import com.oppo.browser.video.common.pubobj.resultObj.news.VideoFeedsListRt;
import com.oppo.browser.video.common.pubobj.resultObj.news.VideoStringRt;
import com.oppo.browser.video.common.service.video.IVideoSourceAdapter;
import com.oppo.browser.video.common.utils.UrlUtils;
import com.oppo.cpc.video.framework.lib.metrics.MonitorUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Slf4j
public class IVideoYouliAdapterImpl implements IVideoSourceAdapter {

//    @Autowired
//    @Qualifier("getVideoUrlBackendExecutor")
//    private GetVideoUrlBackendExecutor getVideoUrlBackendExecutor;

    @Autowired
    @Qualifier("getVideoInfoBackendExecutor")
    private GetVideoInfoBackendExecutor getVideoInfoBackendExecutor;

    @Autowired
    @Qualifier("getBatchVideoInfoBackendExecutor")
    private GetBatchVideoInfoBackendExecutor getBatchVideoInfoBackendExecutor;

    @Autowired
    @Qualifier("getVideoListBackendExecutor")
    private GetVideoListBackendExecutor getVideoListBackendExecutor;

    @Autowired
    @Qualifier("getRecVideoBackendExecutor")
    private GetRecVideoBackendExecutor getRecVideoBackendExecutor;

    @Autowired
    @Qualifier("getMediaInfoBackendExecutor")
    private GetMediaInfoBackendExecutor getMediaInfoBackendExecutor;

    @Autowired
    @Qualifier("getMediaVideoListBackendExecutor")
    private GetMediaVideoListBackendExecutor getMediaVideoListBackendExecutor;

    @Resource
    private MiddleMediaResource middleMediaResource;

    @Autowired
    private YouliApiConfig youliApiConfig;

    @Override
    public VideoStringRt getVideoUrl(VideoURL302ReqProperty nRequest) {
        return null;
    }

    @Override
    public CompletableFuture<VideoStringRt> getVideoUrlV1(VideoURL302ReqProperty reqProperty) {
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public CompletableFuture<VideoFeedsListRt> getListV1(FeedsListReqProperty reqProperty) {
        return getVideoListBackendExecutor.execute(reqProperty);
    }

    @Override
    public CompletableFuture<VideoArticleRt> getVideoInfoV1(VideoInfoReqProperty reqProperty) {
        return CompletableFuture.completedFuture(getVideoInfoBackendExecutor.execute(reqProperty));
    }

    @Override
    public CompletableFuture<VideoFeedsListRt> recVideo(VideoRecListReqProperty reqProperty) {
        reqProperty.setRecVideo(true);
        return getRecVideoBackendExecutor.execute(reqProperty);
    }

    @Override
    public CompletableFuture<VideoArticleMapRt> getBatchVideoInfoV1(BatchArticleInfoReqProperty reqProperty) {
        return getBatchVideoInfoBackendExecutor.execute(reqProperty);
    }

    @Override
    public CompletableFuture<VideoMediaInfoRt> getMediaInfoV1(VideoMediaInfoReqProperty reqProperty) {
        return CompletableFuture.completedFuture(getMediaInfoBackendExecutor.execute(reqProperty));
    }

    @Override
    public CompletableFuture<VideoFeedsListRt> getMediaListV1(VideoMediaListReqProperty reqProperty) {
        return getMediaVideoListBackendExecutor.execute(reqProperty);
    }

    @Override
    public CompletableFuture<NextResponseEntity<VideoCommentList>> getCommentListV1(VideoCommentListReqProperty reqProperty) {
        return null;
    }

    @Override
    public CompletableFuture<NextResponseEntity<VideoQuickCommentList>> getQuickComment(VideoQuickCommentReqProperty reqProperty) {
        return null;
    }

    @Override
    public CompletableFuture<VideoNativeCommentListRt> getReplyList(VideoCommentReqProperty reqProperty) {
        return null;
    }

    @Override
    public CompletableFuture<NextResponseEntity<VideoComment>> addComment(VideoCommentAddReqProperty reqProperty) {
        return null;
    }

    @Override
    public CompletableFuture<NextResponseEntity<VideoLikeComment>> thumbUpComment(VideoCommentLikeReqProperty reqProperty) {
        return null;
    }

    @Override
    public CompletableFuture<VideoBooleanRt> deleteComment(VideoCommentReqProperty reqProperty) {
        return null;
    }

    @Override
    public CompletableFuture<NextResponseEntity<Map<String, VideoMediaInfo>>> getBatchMediaInfo(BatchVideoMediaInfoReqProperty reqProperty) {
        String path = UrlUtils.getUrlPath(youliApiConfig.getAuthorBatchProfileUrl());
        try{
            return middleMediaResource.getBatchMediaInfo(reqProperty).handle((videoMediaInfos, throwable) -> {
                MonitorUtil.counter(MetricConstant.METERICS_VIDEO_YOULI_API_DIFF, videoMediaInfos == null ? reqProperty.getUserIds().size() : reqProperty.getUserIds().size() - videoMediaInfos.size(),
                        MetricConstant.TAG_API, path);
                if(throwable == null){
                    return NextResponseEntity.success(videoMediaInfos);
                }
                Throwable wrappedThrowable = throwable.getCause();
                if(wrappedThrowable instanceof BizRuntimeException){
                    return NextResponseEntity.fail(((BizRuntimeException)wrappedThrowable).getCode(), wrappedThrowable.getMessage());
                }else{
                    log.error("getFeedsList error: ", throwable);
                    return NextResponseEntity.fail(1400, wrappedThrowable.getMessage());
                }
            });
        }catch(Exception e){
            MonitorUtil.counter(MetricConstant.METERICS_VIDEO_YOULI_API_DIFF, reqProperty.getUserIds().size(), MetricConstant.TAG_API, path);
            return CompletableFuture.completedFuture(NextResponseEntity.fail(1400, e.getMessage()));
        }
    }

    @Override
    public VideoBooleanRt updateVideoStatus(VideoUpdateStatusReqProperty reqProperty) {
        return null;
    }

    @Override
    public CompletableFuture<VideoBooleanRt> updateVideoStatusV1(VideoUpdateStatusReqProperty reqProperty) {
        return null;
    }

    @Override
    public CompletableFuture<VideoBooleanRt> dislikeV1(FeedsDislikeReqProperty reqProperty) {
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public CompletableFuture<VideoBooleanRt> accuseV1(FeedsAccuseReqProperty reqProperty) {
        return CompletableFuture.completedFuture(null);
    }
}