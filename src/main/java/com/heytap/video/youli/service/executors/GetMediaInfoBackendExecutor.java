package com.heytap.video.youli.service.executors;

import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import com.oppo.browser.video.common.pubobj.resource.VideoMediaInfoReqProperty;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.VideoMediaInfo;
import com.oppo.browser.video.common.pubobj.resultObj.media.VideoMediaInfoRt;
import com.heytap.video.youli.biz.YouliResourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class GetMediaInfoBackendExecutor {

    @Autowired
    private YouliResourceService youliResourceService;

    public VideoMediaInfoRt execute(VideoMediaInfoReqProperty reqProperty) {
        VideoMediaInfoRt videoMediaInfoRt = new VideoMediaInfoRt();
        videoMediaInfoRt.setRet(0);
        videoMediaInfoRt.setMsg("success");
        try{
            VideoMediaInfo videoMediaInfo = youliResourceService.getMediaInfo(reqProperty);
            videoMediaInfoRt.setData(videoMediaInfo);
        }catch(BizException e){
            log.warn("youliResourceService.getMediaInfo BizException, reqProperty:{}", reqProperty, e);
            videoMediaInfoRt.setRet(e.getCode());
            videoMediaInfoRt.setMsg(e.getMessage());
        }catch(Exception e){
            log.error("youliResourceService.getMediaInfo Exception, reqProperty:{}", reqProperty, e);
            videoMediaInfoRt.setRet(1400);
            videoMediaInfoRt.setMsg(e.getMessage());
        }
        return videoMediaInfoRt;
    }
}
