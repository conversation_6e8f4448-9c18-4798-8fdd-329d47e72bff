package com.heytap.video.youli.service.executors;

import com.heytap.video.youli.biz.LongVideoResource;
import com.heytap.video.youli.biz.YouliResourceService;
import com.heytap.video.youli.metric.YouliMetrics;
import com.oppo.browser.common.pubobj.feeds.runtimeexceptions.BizRuntimeException;
import com.oppo.browser.video.common.pubobj.resource.FeedsListReqProperty;
import com.oppo.browser.video.common.pubobj.resultObj.news.VideoFeedsListRt;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 获取首页推荐内容
 */
@Slf4j
@Component("getVideoListBackendExecutor")
public class GetVideoListBackendExecutor {
    @Autowired
    private LongVideoResource longVideoResource;

    @Autowired
    private YouliResourceService youliResourceService;

    @Autowired
    private YouliMetrics youliMetrics;

    public CompletableFuture<VideoFeedsListRt> execute(FeedsListReqProperty feedsListReqProperty) {

        try{
            return youliResourceService.getFeedsList(feedsListReqProperty).handle((videoFeedsListRt, throwable) -> {
                if(throwable == null){
                    youliMetrics.listCount(videoFeedsListRt,feedsListReqProperty);
                    return videoFeedsListRt;
                }
                VideoFeedsListRt rt = new VideoFeedsListRt();
                Throwable wrappedThrowable = throwable.getCause();
                if(wrappedThrowable instanceof BizRuntimeException){
                    rt.setRet(((BizRuntimeException)wrappedThrowable).getCode());
                    rt.setMsg(wrappedThrowable.getMessage());
                }else{
                    log.error("getFeedsList error: ", throwable);
                    rt.setRet(1400);
                    rt.setMsg(throwable.getMessage());
                }
                return rt;
            });
        } catch (Exception e){
            log.error("getFeedsList error: {}", e.getMessage(), e);
            VideoFeedsListRt videoListRt = new VideoFeedsListRt();
            videoListRt.setRet(1400);
            videoListRt.setMsg(e.getMessage());
            return CompletableFuture.completedFuture(videoListRt);
        }
    }
}
