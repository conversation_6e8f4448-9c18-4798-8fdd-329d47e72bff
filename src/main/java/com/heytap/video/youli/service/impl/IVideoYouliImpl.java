package com.heytap.video.youli.service.impl;

import com.heytap.video.youli.service.executors.GetPondVideosBackendExecutor;
import com.oppo.browser.video.common.pubobj.resource.GetPondVideosReqProperty;
import com.oppo.browser.video.common.pubobj.resultObj.news.VideoFeedsListRt;
import com.oppo.browser.video.common.service.video.IVideoYouli;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.concurrent.CompletableFuture;

@Slf4j
public class IVideoYouliImpl implements IVideoYouli {

    @Autowired
    @Qualifier("getPondVideosBackendExecutor")
    private GetPondVideosBackendExecutor getPondVideosBackendExecutor;

    @Override
    public CompletableFuture<VideoFeedsListRt> getPondVideos(GetPondVideosReqProperty reqProperty) {
        return getPondVideosBackendExecutor.execute(reqProperty);
    }

}
