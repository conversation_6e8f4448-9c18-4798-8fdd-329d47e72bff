package com.heytap.video.youli.service.executors;

import com.heytap.video.youli.biz.YouliResourceService;
import com.oppo.browser.common.pubobj.feeds.runtimeexceptions.BizRuntimeException;
import com.oppo.browser.video.common.pubobj.resource.ListBaseReqProperty;
import com.oppo.browser.video.common.pubobj.resource.VideoRecListReqProperty;
import com.oppo.browser.video.common.pubobj.resultObj.news.VideoFeedsListRt;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

@Component("getRecVideoBackendExecutor")
@Slf4j
public class GetRecVideoBackendExecutor {

    @Autowired
    private YouliResourceService youliResourceService;

    public CompletableFuture<VideoFeedsListRt> execute(VideoRecListReqProperty nRequest) {
        VideoFeedsListRt videoListRt = new VideoFeedsListRt();
        videoListRt.setRet(0);
        videoListRt.setMsg("success");
        try{
            return youliResourceService.getRecVideoV1(nRequest).handle((feedsList, throwable) -> {
                if(throwable == null){
                    videoListRt.setData(feedsList);
                    return videoListRt;
                }
                Throwable wrappedThrowable = throwable.getCause();
                if(wrappedThrowable instanceof BizRuntimeException){
                    log.warn("youliResourceService.getRecVideo exception nRequest:{}", nRequest, throwable);
                    videoListRt.setMsg(wrappedThrowable.getMessage());
                    videoListRt.setRet(((BizRuntimeException)wrappedThrowable).getCode());
                }else{
                    log.error("youliResourceService.getRecVideo error nRequest:{}", nRequest, throwable);
                    videoListRt.setMsg(throwable.getMessage());
                    videoListRt.setRet(1400);
                }
                return videoListRt;
            });
        }catch(Exception e){
            log.warn("youliResourceService.getRecVideo error, nRequest:{}", nRequest, e);
            videoListRt.setRet(1400);
            videoListRt.setMsg("server exception");
        }
        return  CompletableFuture.completedFuture(videoListRt);
    }

}
