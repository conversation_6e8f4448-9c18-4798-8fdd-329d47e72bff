package com.heytap.video.youli.service.executors;

import com.heytap.video.youli.biz.YouliResourceService;
import com.oppo.browser.common.pubobj.feeds.runtimeexceptions.BizRuntimeException;
import com.oppo.browser.video.common.pubobj.resource.VideoMediaListReqProperty;
import com.oppo.browser.video.common.pubobj.resultObj.news.VideoFeedsListRt;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

@Slf4j
@Component("getMediaVideoListBackendExecutor")
public class GetMediaVideoListBackendExecutor {

    @Autowired
    private YouliResourceService youliResourceService;

    public CompletableFuture<VideoFeedsListRt> execute(VideoMediaListReqProperty reqProperty) {
        VideoFeedsListRt videoListRt = new VideoFeedsListRt();
        videoListRt.setRet(0);
        videoListRt.setMsg("success");
        try{
            return youliResourceService.getMediaList(reqProperty).handle((feedsList, throwable) -> {
                if(throwable == null){
                    videoListRt.setData(feedsList);
                    return videoListRt;
                }
                Throwable wrappedThrowable = throwable.getCause();
                if(wrappedThrowable instanceof BizRuntimeException){
                    videoListRt.setRet(((BizRuntimeException)wrappedThrowable).getCode());
                    videoListRt.setMsg(wrappedThrowable.getMessage());
                }else{
                    log.error("getMediaList error: ", throwable);
                    videoListRt.setRet(1400);
                    videoListRt.setMsg(throwable.getMessage());
                }
                return videoListRt;
            });
        } catch (Exception e){
            log.error("getMediaList error: {}", e.getMessage(), e);
            videoListRt.setRet(1400);
            videoListRt.setMsg(e.getMessage());
            return CompletableFuture.completedFuture(videoListRt);
        }

    }

}
