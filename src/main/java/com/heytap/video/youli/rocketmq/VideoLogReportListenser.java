package com.heytap.video.youli.rocketmq;

import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.cpc.video.framework.lib.rocketmq.MyMessageListenerConcurrentlyMgr;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

@Component
public class VideoLogReportListenser implements MessageListenerConcurrently {

    @HeraclesDynamicConfig(key = "rocketmq.consumer.video.horizon.topic", fileName = "rocketmq_consumer.properties")
    private static String topic;

    @PostConstruct
    public void init() {
        MyMessageListenerConcurrentlyMgr.register(topic, this);
    }

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

}
