package com.heytap.video.youli.cache;

import com.heytap.video.youli.mapstruct.AlgorithmDataConvert;
import com.heytap.video.youli.model.algorithm.LvtRecommendAlgorithmData;
import com.heytap.video.youli.model.algorithm.LvtRecommendAlgorithmResponse;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.basic.heracles.client.core.spring.annotation.TextType;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import com.oppo.browser.video.common.pubobj.resource.FeedsListReqProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.Tuple;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AlgorithmPoolCache {

    //public static final String CACHE_KEY = "lv_list:algorithm:{algorithmType}:{docId}:{version}";
    public static final String CACHE_KEY = "youli-serice:algorithm:%s:%s:%s";

    public static final String FALLBACK_TYPE_HTTP_ERROR = "http_error";

    public static final String FALLBACK_TYPE_CONVERT_ERROR = "convert_error";

    @HeraclesDynamicConfig(key = "source.version.mapping", fileName = "program_source_filter.properties", textType = TextType.JSON)
    private Map<String, Integer> sourceAndVersionMapping = new HashMap();

    /**
     * 算法更新开关
     */
    @HeraclesDynamicConfig(key = "algorithm.cache.updateSwitch", fileName = "configure.properties")
    private boolean algorithmCacheUpdateSwitch = true;

    /**
     * 每个key最大的成员数
     */
    @HeraclesDynamicConfig(key = "algorithm.cache.maxSize", fileName = "configure.properties")
    private int algorithmCacheMaxSize = 100;

    /**
     * 成员更新时间
     */
    @HeraclesDynamicConfig(key = "algorithm.cache.updateMin", fileName = "configure.properties")
    private int algorithmCacheUpdateMin = 60;

    /**
     * key过期时间
     */
    @HeraclesDynamicConfig(key = "algorithm.cache.expiredMin", fileName = "configure.properties")
    private int algorithmCacheExpiredMin = 60;

    /**
     * 算法兜底 - redis脚本，判断是否需要更新缓存
     */
    @HeraclesDynamicConfig(key = "algorithm.cache.needUpdateScript", fileName = "configure.properties")
    private String algorithmCacheNeedUpdateScript;

    /**
     * 算法兜底 - redis脚本，删除溢出数据
     */
    @HeraclesDynamicConfig(key = "algorithm.cache.delScript", fileName = "configure.properties")
    private String algorithmCacheDelScript;


    @Autowired
    private JedisCluster jedisCluster;

    @Resource(name = "commonThreadPool")
    private ThreadPoolTaskExecutor commonThreadPool;

    /**
     * 获取算法缓存
     *
     * @param request
     * @return
     */
    public LvtRecommendAlgorithmResponse getAlgorithmCacheResponse(FeedsListReqProperty request, Integer algorithmType, String fallbackType) {
        List<LvtRecommendAlgorithmData> algorithmCacheData = getAlgorithmCacheData(request, algorithmType, fallbackType);
        LvtRecommendAlgorithmResponse LvtRecommendAlgorithmResponse = new LvtRecommendAlgorithmResponse();
        LvtRecommendAlgorithmResponse.setData(algorithmCacheData);
        LvtRecommendAlgorithmResponse.setStatus(0);
        LvtRecommendAlgorithmResponse.setTransparent("cache");
        LvtRecommendAlgorithmResponse.setFallback(true);
        return LvtRecommendAlgorithmResponse;
    }

    /**
     * 获取算法缓存
     *
     * @param request
     * @return
     */
    public List<LvtRecommendAlgorithmData> getAlgorithmCacheData(FeedsListReqProperty request, Integer algorithmType, String fallbackType) {
        String cacheKey = null;
        try {
            cacheKey = buildCacheKey(request, algorithmType);
            log.error("[algorithm fallBack] getAlgorithmCacheData, fallbackType:{}, cacheKey:{}, request：{}",fallbackType, cacheKey, request);
            Integer pageIndex = request.getRefreshTimes();
            Integer pageSize = (request.isFetchMoreVideosSwitch() && null != request.getContentCount())
                    ? request.getContentCount() : request.getLimit();
            if (pageIndex == null || pageSize == null || pageIndex < 0 || pageSize < 1) {
                log.error("getAlgorithmCacheData error, pageIndex or pageSize is error, cacheKey:{}, request：{}", cacheKey, request);
                return new ArrayList<>();
            }
            pageIndex = pageIndex + 1;
            //页数超出时兼容
            if (pageIndex != 1) {
                int count = jedisCluster.zcard(cacheKey).intValue();
                if (count <= 0) {
                    log.error("getAlgorithmCacheData error, redis count is 0, cacheKey:{}, request：{}", cacheKey, request);
                    return new ArrayList<>();
                }
                //当前数据总页数
                int totalPage = (int) Math.ceil((double) count / pageSize);
                //超出内容总数时，从第一页循环取
                if (pageIndex > totalPage) {
                    pageIndex = (pageIndex - 1) % totalPage + 1;
                }
            }
            int offset = (pageIndex - 1) * pageSize;
            //按排名获取对应页数数据
            Set<Tuple> tuples = jedisCluster.zrevrangeByScoreWithScores(cacheKey, Double.MAX_VALUE, -1, offset, pageSize);
            if (tuples == null || tuples.isEmpty()) {
                log.error("getAlgorithmCacheData error, redis is null or empty, cacheKey:{}, request：{}", cacheKey, request);
                return new ArrayList<>();
            }
            String finalCacheKey = cacheKey;
            return tuples.stream().map(tuple -> {
                LvtRecommendAlgorithmData LvtRecommendAlgorithmData = JsonUtil.fromStr(tuple.getElement(), LvtRecommendAlgorithmData.class);
                if (LvtRecommendAlgorithmData != null) {
                    LvtRecommendAlgorithmData.setScore(tuple.getScore());
                } else {
                    log.error("getAlgorithmCacheData error, json convert error, json：{}, cacheKey:{}, request：{}", finalCacheKey, tuple.getElement(), request);
                }
                return LvtRecommendAlgorithmData;
            }).filter(Objects::nonNull).sorted(Comparator.comparingDouble(LvtRecommendAlgorithmData::getScore)).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("[algorithm fallBack] getAlgorithmCacheData error, cacheKey:{}, request：{}, e", cacheKey, request, e);
            return new ArrayList<>();
        }
    }

    /**
     * 添加算法缓存成员
     *
     * @param request
     * @param response
     */
    public void addAlgorithmCacheMember(FeedsListReqProperty request, Integer algorithmType, LvtRecommendAlgorithmResponse response) {
        CompletableFuture.runAsync(() -> {
            try {
                if(!algorithmCacheUpdateSwitch){
                    return;
                }
                if (request == null || response == null) {
                    return;
                }
                String cacheKey = buildCacheKey(request, algorithmType);
                if (!needUpdate(cacheKey)) {
                    return;
                }
                log.info("addAlgorithmCacheMember, cacheKey：{}, request：{}, response:{}", cacheKey, request, response);
                //使用当前时间戳为分数
                Double score = Double.parseDouble(String.valueOf(System.currentTimeMillis()));
                Map<String, Double> scoreMembers = response.getData().stream().filter(item -> item != null && StringUtils.isNotEmpty(item.getId()))
                        .collect(Collectors.toMap(item -> JsonUtil.toJson(AlgorithmDataConvert.INSTANCE.toCacheData(item)), item -> score, (a, b) -> a));
                jedisCluster.zadd(cacheKey, scoreMembers);
                //只保留100条数据，移除时间戳较小的数据
                jedisCluster.eval(algorithmCacheDelScript, 1, cacheKey, String.valueOf(algorithmCacheMaxSize));
                jedisCluster.expire(cacheKey, algorithmCacheExpiredMin * 60);
            } catch (Exception e) {
                log.error("addAlgorithmCacheMember error, request：{}, response:{}", request, response, e);
            }
        }, commonThreadPool);
    }

    /**
     * 是否需要更新缓存数据
     */
    private boolean needUpdate(String cacheKey) {
        long expireTime = System.currentTimeMillis() - algorithmCacheUpdateMin * 60L * 1000L;
        Object result = jedisCluster.eval(algorithmCacheNeedUpdateScript, 1, cacheKey, String.valueOf(algorithmCacheMaxSize), String.valueOf(expireTime));
        return result.equals(1L);
    }

    /**
     * 获取算法缓存key
     *
     * @param request
     * @return
     */
    public String buildCacheKey(FeedsListReqProperty request, Integer algorithmType) {
        return String.format(CACHE_KEY,
                algorithmType,
                StringUtils.defaultString(request.getSid()),
                StringUtils.defaultString(buildCacheVersion(request.getVersion())));
    }

    /**
     * 获取缓存版本号
     *
     * @param version
     * @return
     */
    private String buildCacheVersion(Integer version) {
        if (version == null || version < 0) {
            return "0";
        }
        List<Integer> versionList = sourceAndVersionMapping.values().stream().sorted().collect(Collectors.toList());
        if (versionList.contains(version)) {
            return version.toString();
        }

        for (int i = 0; i < versionList.size(); i++) {
            Integer minVersion = i == 0 ? 0 : versionList.get(i - 1);
            Integer maxVersion = versionList.get(i);
            if (version.compareTo(minVersion) > 0 && version.compareTo(maxVersion) < 0) {
                return minVersion.toString();
            }
        }

        return versionList.get(versionList.size() - 1).toString();
    }
}
