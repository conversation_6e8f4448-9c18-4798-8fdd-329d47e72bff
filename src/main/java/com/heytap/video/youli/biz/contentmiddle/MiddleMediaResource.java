package com.heytap.video.youli.biz.contentmiddle;

import com.heytap.video.youli.config.ResourceConfig;
import com.heytap.video.youli.config.YouliApiConfig;
import static com.heytap.video.youli.constant.GlobalConstant.APPID;
import com.heytap.video.youli.metric.MetricConstant;
import com.heytap.video.youli.model.content.BatchMediaInfoResult;
import com.heytap.video.youli.model.content.MediaCountInfo;
import com.heytap.video.youli.model.content.MediaInfo;
import com.heytap.video.youli.model.content.MediaInfoResult;
import com.heytap.video.youli.model.content.ResourceResult;
import com.heytap.video.youli.model.content.VideoResource;
import com.oppo.browser.app.framework.utils.MyHttpUtils;
import com.oppo.browser.common.app.lib.encryption.JsonTools;
import com.oppo.browser.common.app.lib.http.Request;
import com.oppo.browser.common.app.lib.utils.SysInfo;
import com.oppo.browser.common.app.lib.utils.UrlTools;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import com.oppo.browser.common.pubobj.feeds.exceptions.InvalidDataException;
import com.oppo.browser.common.pubobj.feeds.runtimeexceptions.InvalidDataRuntimeException;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import com.oppo.browser.dfoob.channel.HttpDataChannelException;
import com.oppo.browser.video.common.pubobj.FeedsConstant;
import com.oppo.browser.video.common.pubobj.resource.BatchVideoMediaInfoReqProperty;
import com.oppo.browser.video.common.pubobj.resource.VideoMediaInfoReqProperty;
import com.oppo.browser.video.common.pubobj.resource.VideoMediaListReqProperty;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.VideoMediaInfo;
import com.oppo.browser.video.common.utils.UrlUtils;
import com.oppo.cpc.video.framework.lib.metrics.MonitorUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpRequestBase;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MiddleMediaResource {
    @Autowired
    private YouliApiConfig youliApiConfig;

    @Autowired
    private HttpDataChannel httpDataChannel;

    public VideoMediaInfo getMediaInfo(VideoMediaInfoReqProperty reqProperty) throws BizException {

        Map<String, String> params = getReqMediaInfoParams(reqProperty);
        String url = youliApiConfig.getAuthorProfileUrl() + "?" + UrlTools.httpBuildQuery(params);
        HttpGet get = new HttpGet(url);
        Request.addHeader(get, getCommonHeaders());
        String json = MyHttpUtils.execute(get, RequestConfig.custom().setConnectTimeout(200).setConnectionRequestTimeout(100)
                .setSocketTimeout(youliApiConfig.getAuthorProfileTimeOut()).build());
        MediaInfo mediaInfo = handleHttpResult(json, get);
        VideoMediaInfo videoMediaInfo = new VideoMediaInfo();
        videoMediaInfo.setFollowStatus(true);
        videoMediaInfo.setMediaId(reqProperty.getMediaNo());
        videoMediaInfo.setName(mediaInfo.getName());
        videoMediaInfo.setSummary(mediaInfo.getSummary());
        videoMediaInfo.setMediaSource(FeedsConstant.TopResource.YOULI);
        MediaCountInfo count = mediaInfo.getCount();
        if(count != null){
            videoMediaInfo.setFollowers((int) count.getFollower());
            videoMediaInfo.setVideos((int) count.getResource());
            videoMediaInfo.setViews(count.getClick());
        }
        if (!StringUtils.isEmpty(mediaInfo.getAvatar())) {
            videoMediaInfo.setImage(mediaInfo.getAvatar());
        } else {
            videoMediaInfo.setImage(ResourceConfig.getDefaultAuthorAvatar());
        }
        videoMediaInfo.setOff(false);
        return videoMediaInfo;
    }

    public CompletableFuture<Map<String, VideoMediaInfo>> getBatchMediaInfo(BatchVideoMediaInfoReqProperty reqProperty) {
        if (CollectionUtils.isEmpty(reqProperty.getUserIds())){
            return CompletableFuture.completedFuture(new HashMap<>());
        }

        return getMediaInfoList(reqProperty.getUserIds()).thenApply((mediaInfoList) -> {
            Map<String, VideoMediaInfo> mediaInfoMap = new HashMap<>();
            for (MediaInfo info : mediaInfoList) {
                VideoMediaInfo mediaInfo = new VideoMediaInfo();
                mediaInfo.setMediaId(info.getAuthorId());
                mediaInfo.setName(info.getName());
                mediaInfo.setImage(info.getAvatar());
                mediaInfo.setSummary(info.getSummary());
                mediaInfo.setFollowStatus(true);
                mediaInfo.setMediaSource(FeedsConstant.TopResource.YOULI);
                mediaInfo.setOff(false);
                if (info.getCount() != null) {
                    mediaInfo.setVideos((int) info.getCount().getResource());
                    mediaInfo.setFollowers((int) info.getCount().getFollower());
                    mediaInfo.setViews(info.getCount().getClick());
                }
                mediaInfoMap.put(info.getAuthorId(), mediaInfo);
            }
            return mediaInfoMap;
        });
    }

    private CompletableFuture<List<MediaInfo>> getMediaInfoList(List<String> authorIds) {
        if (CollectionUtils.isEmpty(authorIds)){
            return CompletableFuture.completedFuture(new ArrayList<>());
        }
        Map<String, Object> params = new HashMap<>();
        params.put("authorIds", authorIds);
        params.put(APPID, 2);

        CompletableFuture<BatchMediaInfoResult> resultCf;
        try {
            resultCf = httpDataChannel.asyncPostForObject(youliApiConfig.getAuthorBatchProfileUrl(), JsonTools.toJsonString(params),
                    BatchMediaInfoResult.class, null, getCommonHeaders(), youliApiConfig.getAuthorBatchProfileTimeOut());
        } catch (HttpDataChannelException e) {
            resultCf = CompletableFuture.completedFuture(null);
            log.error("get batch media info fail: error msg:{}", e.getMessage());
        }

        return resultCf.handle((result, throwable) -> {
            if ((throwable != null) || (result == null) || (result.getCode() != 0)) {
                log.warn("get batch media info fail:url[{}], params[{}], jsonResult[{}]", youliApiConfig.getAuthorBatchProfileUrl(), params, result);
                throw new InvalidDataRuntimeException();
            }
            if((result.getData() == null) || CollectionUtils.isEmpty(result.getData().getProfile())){
                return new ArrayList<>();
            }
            return result.getData().getProfile();
        });
    }

    public CompletableFuture<List<VideoResource>> getMediaResourceList(VideoMediaListReqProperty reqProperty) {
        Map<String, Object> params = getMediaListReqParams(reqProperty);

        CompletableFuture<ResourceResult> future;
        try {
            future = httpDataChannel.asyncPostForObject(youliApiConfig.getAuthorVideosUrl(), JsonTools.toJsonString(params), ResourceResult.class,
                    null, getCommonHeaders(), youliApiConfig.getAuthorVideosTimeOut());
        } catch (Exception e) {
            log.error("media video list fail:url[{}], params[{}]", youliApiConfig.getAuthorVideosUrl(), params, e);
            future = CompletableFuture.completedFuture(null);
        }
        return future.handle((result, throwable) -> {
            if (result == null || (result.getCode() != 0 && result.getCode() != 4) || (result.getCode() == 0 && result.getData() == null)) {
                log.warn("media video list fail:url[{}], params[{}], result[{}]", youliApiConfig.getAuthorVideosUrl(), params, result);
                return Collections.emptyList();
            }
            return result.getData() == null ?  Collections.emptyList() : result.getData().getResourceList();
        });
    }

    private Map<String, String> getReqMediaInfoParams(VideoMediaInfoReqProperty reqProperty) {
        Map<String, String> paraMap = new HashMap<>();
        paraMap.put("authorId", reqProperty.getMediaNo());
        paraMap.put(APPID, "2");
        return paraMap;
    }

    private MediaInfo handleHttpResult(String json, HttpRequestBase requestBase) throws BizException {
        MediaInfoResult mediaInfoResult = JsonTools.toMap(json, MediaInfoResult.class);
        if ((mediaInfoResult == null) || (mediaInfoResult.getCode() != 0) || (mediaInfoResult.getData().getProfile() == null)) {
            if (log.isWarnEnabled()) {
                log.warn("mediaProfile_api :[url:{}][header :{} ][rt:{}]", requestBase.getURI(), requestBase.getAllHeaders(),
                        json);
            }
            MonitorUtil.counter(MetricConstant.METERICS_VIDEO_YOULI_API_DIFF, 1, MetricConstant.TAG_API, UrlUtils.getUrlPath(youliApiConfig.getAuthorProfileUrl()));
            throw new InvalidDataException();
        }
        return mediaInfoResult.getData().getProfile();
    }

    private Map<String, Object> getMediaListReqParams(VideoMediaListReqProperty reqProperty) {
        Map<String, Object> params = new HashMap<>();
        params.put(APPID, 2);
        params.put("authorId", reqProperty.getMediaNo());
        params.put("type", "0".equals(String.valueOf(reqProperty.getType())) ? String.valueOf(2) : String.valueOf(4));
        params.put("limit", reqProperty.getNumber() >= 15 ? 15 : reqProperty.getNumber());
        params.put("ip", StringUtils.defaultString(reqProperty.getAttributeValues().getIp()));
        params.put("network", StringUtils.defaultString(reqProperty.getAttributeValues().getNetwork()));
        params.put("saveMode", 0);
        if (reqProperty.getAttributeValues().getBuuid() != 0) {
            params.put("buuid", String.valueOf(reqProperty.getAttributeValues().getBuuid()));
        }
        if (reqProperty.getOffset() >= 0) {//5.14优化start+limit方式拉取
            params.put("start", reqProperty.getOffset());
            if(StringUtils.isNotEmpty(reqProperty.getLastDocid())){
                params.put("lastId", reqProperty.getLastDocid());
            }
            if (reqProperty.getLastPublishTime() != null) {
                params.put("lastPublishTime", reqProperty.getLastPublishTime());
            }
        } else if (reqProperty.getPageNum() > 0) {
            params.put("pageNum", reqProperty.getPageNum());
            params.put("offset", 0);
        } else {
            String beHotTime = reqProperty.getBeHotTime();
            params.put("offset", (StringUtils.isEmpty(beHotTime) || "0".equals(beHotTime)) ? String.valueOf(SysInfo.getUnixTimeStamp()) : beHotTime);
        }
        return params;
    }

    private Map<String, String> getCommonHeaders() {
        Map<String, String> headers = new HashMap<>(2);
        headers.put(APPID, "2");
        headers.put("Content-Type", "application/json");
        return headers;
    }

}
