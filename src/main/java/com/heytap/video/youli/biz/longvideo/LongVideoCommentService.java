package com.heytap.video.youli.biz.longvideo;


import com.heytap.video.youli.biz.longvideo.comment.CommentCountRequest;
import com.heytap.video.youli.biz.longvideo.comment.CommentCountResponse;
import com.heytap.video.youli.config.LongVideoConfig;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class LongVideoCommentService {
    @Autowired
    private HttpDataChannel httpDataChannel;

    @Autowired
    private LongVideoConfig longVideoConfig;

    public CompletableFuture<Map<String, Integer>> getCommentCountAsync(String virtualSids) {
        CommentCountRequest request = new CommentCountRequest();
        request.setDocIds(virtualSids);
        request.setBusinessType(longVideoConfig.getCommentBusinessType());
        CompletableFuture<String> responseCf;

        try {
            responseCf = httpDataChannel.asyncPostForObject(longVideoConfig.getCommentCountUrl(), request,
                    String.class, longVideoConfig.getCommentCountTimeout());
        } catch (Exception e) {
            log.error("error get comment count:{}", request, e);
            return CompletableFuture.completedFuture(new HashMap<>());
        }

        return responseCf.handle((response, e) -> {
            if (e != null) {
                log.error("error get comment count,response:{}", response, e);
                return new HashMap<>();
            }
            CommentCountResponse commentCountResponse = JsonUtil.fromStr(response, CommentCountResponse.class);
            if (response == null || MapUtils.isEmpty(commentCountResponse.getData())) {
                log.warn("get comment count return invalid response:{},{}", request, response);
                return new HashMap<>();
            }
            log.info("virtualSids: {}, response: {}", virtualSids, commentCountResponse);
            return commentCountResponse.getData();
        });
    }
}