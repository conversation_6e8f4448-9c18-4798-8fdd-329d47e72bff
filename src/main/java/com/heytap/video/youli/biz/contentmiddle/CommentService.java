package com.heytap.video.youli.biz.contentmiddle;

import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.browser.common.app.lib.encryption.JsonTools;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import com.oppo.browser.dfoob.channel.HttpDataChannelException;
import com.oppo.browser.video.common.pubobj.FeedsConstant;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.Article;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CommentService {
    @Autowired
    private HttpDataChannel httpDataChannel;

    @HeraclesDynamicConfig(key = "commentSwitch", fileName = "comment.properties")
    private static String commentSwitch = "no";

    @HeraclesDynamicConfig(key = "commentUrl", fileName = "comment.properties")
    private static String commentUrl;

    @HeraclesDynamicConfig(key = "batchQueryPath", fileName = "comment.properties")
    private static String batchQueryPath;

    @HeraclesDynamicConfig(key = "timeout", fileName = "comment.properties")
    private static Integer timeout = 512;

    public void setCommentCount(List<Article> articles) {
        if ("no".equals(commentSwitch)){
            return;
        }
        log.debug("setCommentCount article list:{}", articles);
        if (CollectionUtils.isEmpty(articles)) {
            return;
        }
        List<String> doIds = new ArrayList<>();
        for (Article article : articles) {
            doIds.add(article.getId());
        }

        Map<String, Integer> cmtCount = null;
        try {
            cmtCount = getArticlesCmtCountAsync(doIds).get();
        } catch (Exception e) {
            log.warn("getArticlesCmtCountAsync.get error: {}", doIds, e);
        }
        setArticlesCmtCount(articles, cmtCount);
    }

    public void setArticlesCmtCount(List<Article> articles, Map<String, Integer> cmtCount) {
        if(CollectionUtils.isEmpty(articles) || MapUtils.isEmpty(cmtCount)){
            return;
        }

        Integer count;
        for (Article article : articles) {
            count = cmtCount.get(article.getId());
            if (count != null) {
                article.setCmtCnt(count);
            }
        }
    }

    public CompletableFuture<Map<String, Integer>> getArticlesCmtCountAsync(List<String> docIds) {
        log.debug("getArticlesCmtCountAsync docIds:{}", docIds);
        if ("no".equals(commentSwitch) || CollectionUtils.isEmpty(docIds)) {
            return CompletableFuture.completedFuture(null);
        }

        StringBuilder sb = new StringBuilder();
        for (String docId : docIds) {
            sb.append(docId).append(",");
        }
        String docIdsParam = sb.deleteCharAt(sb.lastIndexOf(",")).toString();
        Map<String, String> headers = new HashedMap<>();
        headers.put("Content-type", "application/json; charset=UTF-8");
        Map<String, String> params = new HashedMap<>();
        params.put("businessType", "VIDEO");
        params.put("docIds", docIdsParam);
        params.put("docId", "fake");
        params.put("source", FeedsConstant.TopResource.YOULI);
        String jsonParam = JsonTools.toJsonString(params);
        CompletableFuture<String> cf;
        try {
            cf = httpDataChannel.asyncPostForObject(commentUrl + batchQueryPath, jsonParam, String.class, null, headers, timeout);
        } catch (HttpDataChannelException e) {
            log.warn("queryBatchCommentCount error,docIds:{}", docIds, e);
            cf = CompletableFuture.completedFuture(null);
        }
        return cf.handle((jsonResult, throwable) -> {
            log.debug("request:{}, jsonResult:{}", jsonParam, jsonResult);
            if(throwable != null || StringUtils.isEmpty(jsonResult)){
                log.warn("queryBatchCommentCount error,docIds:{}", docIds, throwable);
                return null;
            }
            Map<String, Object> result = JsonTools.toMap(jsonResult, Map.class);
            Integer rt = (Integer) result.get("code");
            Map<String, Integer> data = (Map<String, Integer>) result.get("data");

            if (rt != null && rt == 0 && data != null) {
                return data;
            }
            log.warn("queryBatchCommentCount error docIds:{}, jsonResult:{}", docIds, jsonResult);
            return null;
        });
    }

}