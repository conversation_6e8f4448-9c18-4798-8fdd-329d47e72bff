package com.heytap.video.youli.biz;

import com.heytap.longvideo.client.arrange.entity.LvContentTemplateItem;
import com.heytap.video.fallback.model.FallbackResponse;
import com.heytap.video.fallback.util.FallbackDataUtil;
import com.heytap.video.youli.biz.algorithm.LongVideoTrailerRecommendService;
import com.heytap.video.youli.biz.algorithm.RecommendService;
import com.heytap.video.youli.biz.contentmiddle.CommentService;
import com.heytap.video.youli.biz.contentmiddle.MiddleMediaResource;
import com.heytap.video.youli.biz.contentmiddle.MiddlePondResource;
import com.heytap.video.youli.biz.contentmiddle.MiddleVideoResource;
import com.heytap.video.youli.cache.AlgorithmPoolCache;
import com.heytap.video.youli.config.LongVideoConfig;
import com.heytap.video.youli.config.LongVideoTrailerRecommendConfig;
import com.heytap.video.youli.config.ResourceConfig;
import com.heytap.video.youli.config.YouliApiConfig;
import com.heytap.video.youli.constant.Bidlst;
import com.heytap.video.youli.metric.MetricConstant;
import com.heytap.video.youli.metric.YouliMetrics;
import com.heytap.video.youli.model.ExtraInfo;
import com.heytap.video.youli.model.QualityComparable;
import com.heytap.video.youli.model.algorithm.LvtRecommendAlgorithmResponse;
import com.heytap.video.youli.model.algorithm.Recommend;
import com.heytap.video.youli.model.algorithm.RecommendList;
import com.heytap.video.youli.model.content.Author;
import com.heytap.video.youli.model.content.FilterWord;
import com.heytap.video.youli.model.content.VideoCountInfo;
import com.heytap.video.youli.model.content.VideoDetailInfo;
import com.heytap.video.youli.model.content.VideoInfo;
import com.heytap.video.youli.model.content.VideoResource;
import com.heytap.video.youli.utils.BizUtils;
import com.oppo.browser.app.framework.utils.MyHttpUtils;
import com.oppo.browser.app.framework.utils.MyListUtils;
import com.oppo.browser.common.app.lib.encryption.JsonTools;
import com.oppo.browser.common.app.lib.protobuf.StringBuilderHolder;
import com.oppo.browser.common.pubobj.feeds.exceptions.BadRequestException;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import com.oppo.browser.video.common.pubobj.FeedsConstant;
import com.oppo.browser.video.common.pubobj.constant.RelationTypeConstant;
import com.oppo.browser.video.common.pubobj.constant.StyleTypeConstant;
import com.oppo.browser.video.common.pubobj.resource.*;
import com.oppo.browser.video.common.pubobj.resultObj.ad.AppInfo;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.Article;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.FeedsList;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.Item;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.Medium;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.ReasonObj;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.RelationInfo;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.Video;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.VideoMediaInfo;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.VideoUrl;
import com.oppo.browser.video.common.pubobj.resultObj.news.VideoFeedsListRt;
import com.oppo.cpc.video.framework.lib.appstore.AppDetailInfo;
import com.oppo.cpc.video.framework.lib.appstore.AppStoreService;
import com.oppo.cpc.video.framework.lib.exception.BadRequestRuntimeException;
import com.oppo.cpc.video.framework.lib.metrics.MonitorUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.heytap.video.youli.cache.AlgorithmPoolCache.FALLBACK_TYPE_CONVERT_ERROR;
import static com.heytap.video.youli.constant.GlobalConstant.R_ACTION;
import static com.heytap.video.youli.constant.GlobalConstant.R_PAGE;

@Slf4j
@Service
public class YouliResourceService {
    @Autowired
    private RecommendService recommendService;

    @Autowired
    private LongVideoTrailerRecommendService lvtRecommendService;

    @Autowired
    private YouliMetrics youliMetrics;

    @Autowired
    private MiddleVideoResource middleVideoResource;

    @Autowired
    private MiddleMediaResource middleMediaResource;

    @Autowired
    private CommentService commentService;

    @Autowired
    private MiddlePondResource middlePondResource;

    @Autowired
    private AppStoreService appStoreService;

    @Autowired
    private YouliApiConfig youliApiConfig;

    @Autowired
    private LongVideoTrailerRecommendConfig longVideoTrailerRecommendConfig;

    @Autowired
    private LongVideoResource longVideoResource;

    @Autowired
    private LongVideoConfig longVideoConfig;

    @Autowired
    private AlgorithmPoolCache algorithmPoolCache;


//    private static final int EDU_LONG_VIDEO_SUPPORT_MIN_VERSION = 48;
//    private static final String EDU_LONG_VIDEO_RELATED_TYPE = "eduLongVideo";

    /**
     * 获取各频道视频列表
     *
     * @param reqProperty
     * @return
     * @throws BizException
     */
    public CompletableFuture<VideoFeedsListRt> getFeedsList(FeedsListReqProperty reqProperty) {
        //直接获取中台内容池的频道
        if (MiddlePondResource.pondChannelPondIdMap.containsKey(reqProperty.getFromId())) {
            return middlePondResource.getPondChannelVideos(reqProperty).thenCompose(pondVideos -> handlePondVideos(reqProperty, pondVideos, true));
        }
        if (reqProperty.getFromId().equals(longVideoTrailerRecommendConfig.getRecommendAlgorithmFromId())) {
            //自荐周边视频算法推荐
            return lvtRecommendService.getRecommendAlgorithmDatas(reqProperty).thenCompose(response ->
                    longVideoResource.handleResponse(reqProperty, response).thenCompose(feedsListRt -> {
                Integer algorithmType = null;
                if (StringUtils.isNotEmpty(reqProperty.getSid()) && StringUtils.isNotEmpty(reqProperty.getVid())) {
                    if (reqProperty.getImmersionTrailerScene() != null && reqProperty.getImmersionTrailerScene() == 1 &&
                            StringUtils.isNotBlank(reqProperty.getTrailerOperationCode()) && reqProperty.getOriginalChannel() != null) {
                        //详情页周边视频进入沉浸式，将周边视频列表通过getList接口下发
                    }else{
                        algorithmType = longVideoConfig.getAlgorithmTypeOne();
                    }
                } else if (StringUtils.isNotEmpty(reqProperty.getPoolCode())) { //内容池周边视频推荐 B1655276899006 场景
                    algorithmType = longVideoConfig.getAlgorithmTypeTwo();
                }
                //非算法场景直接返回
                if(algorithmType == null){
                    return CompletableFuture.completedFuture(feedsListRt);
                }
                //数据正常直接返回
                if(feedsListRt != null && feedsListRt.getData() != null && CollectionUtils.isNotEmpty(feedsListRt.getData().getLongVideos())){
                    //非兜底数据更新缓存
                    if(response != null && !response.isFallback()){
                        algorithmPoolCache.addAlgorithmCacheMember(reqProperty, algorithmType, response);
                    }
                    return CompletableFuture.completedFuture(feedsListRt);
                }
                //数据解析失败走兜底缓存
                //如果已经是兜底数据，不再重复兜底
                if(response != null && response.isFallback()){
                    return CompletableFuture.completedFuture(feedsListRt);
                }
                //获取缓存数据
                LvtRecommendAlgorithmResponse algorithmCacheResponse = algorithmPoolCache.getAlgorithmCacheResponse(reqProperty, algorithmType, FALLBACK_TYPE_CONVERT_ERROR);
                if(algorithmCacheResponse != null && CollectionUtils.isNotEmpty(algorithmCacheResponse.getData())){
                    return longVideoResource.handleResponse(reqProperty, algorithmCacheResponse);
                }else{
                    log.warn("[trailerRecommendPool] getFallbackData is null, request:{}", reqProperty);
                }

                return CompletableFuture.completedFuture(feedsListRt);
            }));
        }
        // 如果是芒果UGC也是先走算法推荐，需要把内容池code带给算法
        if ("ugc".equalsIgnoreCase(reqProperty.getFromId())) {
            //自荐UGC视频算法推荐
            if (StringUtils.isBlank(reqProperty.getPoolCode())) {
                reqProperty.setPoolCode(longVideoTrailerRecommendConfig.getDefaultUgcPool());
            }
            return lvtRecommendService.requestAlgorithm(reqProperty, longVideoConfig.getAlgorithmTypeUGC()).thenCompose(response ->
                    longVideoResource.handleUgc(reqProperty, response).thenCompose(feedsListRt -> {
                        //数据正常直接返回
                        if(feedsListRt != null && feedsListRt.getData() != null && CollectionUtils.isNotEmpty(feedsListRt.getData().getLongVideos())){
                            //非兜底数据更新缓存
                            if(response != null && !response.isFallback()){
                                algorithmPoolCache.addAlgorithmCacheMember(reqProperty, longVideoConfig.getAlgorithmTypeUGC(), response);
                            }
                            return CompletableFuture.completedFuture(feedsListRt);
                        }
                        //数据解析失败走兜底缓存
                        //如果已经是兜底数据，不再重复兜底
                        if(response != null && response.isFallback()){
                            return CompletableFuture.completedFuture(feedsListRt);
                        }
                        //获取缓存数据
                        LvtRecommendAlgorithmResponse algorithmCacheResponse = algorithmPoolCache.getAlgorithmCacheResponse(reqProperty, longVideoConfig.getAlgorithmTypeUGC(), FALLBACK_TYPE_CONVERT_ERROR);
                        if(algorithmCacheResponse != null && CollectionUtils.isNotEmpty(algorithmCacheResponse.getData())){
                            return longVideoResource.handleUgc(reqProperty, algorithmCacheResponse);
                        }else{
                            log.warn("[ugcPool] getFallbackData is null, request:{}", reqProperty);
                        }

                        return CompletableFuture.completedFuture(feedsListRt);
                    }));
        }
        // 如果是风行短视频
        if ("fengxingShort".equalsIgnoreCase(reqProperty.getFromId())) {
            // 低于7.13版本不下发风行短视频内容
            if (reqProperty.getVersion() < 71300) {
                return CompletableFuture.completedFuture(null);
            }

            //自荐风行短视频算法推荐
            if (StringUtils.isBlank(reqProperty.getPoolCode())) {
                reqProperty.setPoolCode(longVideoTrailerRecommendConfig.getDefaultFxsPool());
            }
            return lvtRecommendService.requestAlgorithm(reqProperty, longVideoConfig.getAlgorithmTypeFXS()).thenCompose(response ->
                    longVideoResource.handleFxShort(reqProperty, response).thenCompose(feedsListRt -> {
                        //数据正常直接返回
                        if(feedsListRt != null && feedsListRt.getData() != null && CollectionUtils.isNotEmpty(feedsListRt.getData().getLongVideos())){
                            //非兜底数据更新缓存
                            if(response != null && !response.isFallback()){
                                algorithmPoolCache.addAlgorithmCacheMember(reqProperty, longVideoConfig.getAlgorithmTypeFXS(), response);
                            }
                            return CompletableFuture.completedFuture(feedsListRt);
                        }
                        //数据解析失败走兜底缓存
                        //如果已经是兜底数据，不再重复兜底
                        if(response != null && response.isFallback()){
                            return CompletableFuture.completedFuture(feedsListRt);
                        }
                        //获取缓存数据
                        LvtRecommendAlgorithmResponse algorithmCacheResponse = algorithmPoolCache.getAlgorithmCacheResponse(reqProperty, longVideoConfig.getAlgorithmTypeFXS(), FALLBACK_TYPE_CONVERT_ERROR);
                        if(algorithmCacheResponse != null && CollectionUtils.isNotEmpty(algorithmCacheResponse.getData())){
                            return longVideoResource.handleFxShort(reqProperty, algorithmCacheResponse);
                        }else{
                            log.warn("[fxShortPool] getFallbackData is null, request:{}", reqProperty);
                        }

                        return CompletableFuture.completedFuture(feedsListRt);
                    }));
        }
        return recommendService.getFeedsRecommend(reqProperty).thenApply(response -> handleRecommendFallbackResponse(reqProperty, response));
    }

    private CompletableFuture<VideoFeedsListRt> handlePondVideos(FeedsListReqProperty reqProperty, List<VideoResource> pondVideos, boolean isChannel) {
        List<String> videoIds = new LinkedList<>();
        if (CollectionUtils.isNotEmpty(pondVideos)) {
            for (VideoResource resource : pondVideos) {
                videoIds.add(resource.getId());
            }
        }
        FeedsList feedsList = getFromContentMiddle(reqProperty, pondVideos, null);
        return commentService.getArticlesCmtCountAsync(videoIds).handle((cmtCount, throwable) -> {
            commentService.setArticlesCmtCount(feedsList.getArticles(), cmtCount);
            if (CollectionUtils.isNotEmpty(feedsList.getArticles()) && isChannel) {
                feedsList.getArticles().get(feedsList.getArticles().size() - 1).setBeHotTime(reqProperty.getBottomtime().longValue());
            }
            VideoFeedsListRt rt = new VideoFeedsListRt();
            rt.setRet(0);
            rt.setMsg("success");
            rt.setData(feedsList);
            return rt;
        });
    }

    private VideoFeedsListRt handleRecommendFallbackResponse(FeedsListReqProperty reqProperty, FallbackResponse<RecommendList, FeedsList> response) {
        VideoFeedsListRt rt = new VideoFeedsListRt();
        if (response == null || (!response.isFallbackData() && response.getValue() == null) || (response.isFallbackData() && response.getFallbackValue() == null)) {
            log.warn("empty FeedsRecommendResponse, req:{}, response:{}", "", response);
            rt.setRet(1400);
            rt.setMsg("empty FeedsRecommendResponse[{}][{}][{}]");
            return rt;
        }
        if (response.isFallbackData()) {
            youliMetrics.recommendFallbackReport(response.getFallbackValue(), reqProperty);
            rt.setData(response.getFallbackValue());
            rt.setRet(0);
            return rt;
        }
        RecommendList recommendList = response.getValue();
        youliMetrics.recommendApiRetReport(recommendList, reqProperty);
        if ((recommendList == null) || (recommendList.getCode() != 0) || MyListUtils.isEmpty(recommendList.getResult())) {
            log.warn("recommend_api :[url:{}][rt:{}]", "", response.getValue());
            rt.setRet(1400);
            rt.setMsg("recommend result:" + (recommendList != null ? (recommendList.getMessage() + "[code:" + recommendList.getCode() + "]") : null));
            return rt;
        }
        ExtraInfo extraInfo = buildRecommendExtraInfo(recommendList, Bidlst.CHANNEL);
        rt.setRet(0);
        rt.setMsg("success");
        rt.setData(getFromContentMiddle(reqProperty, recommendList.getResult(), extraInfo));
        writeFallbackData(reqProperty, rt.getData());
        return rt;
    }


    private ExtraInfo buildRecommendExtraInfo(RecommendList recommendList, String bidlst) {
        ExtraInfo extraInfo = new ExtraInfo();
        extraInfo.setRid(recommendList.getRid());
        extraInfo.setBidlst(bidlst);
        return extraInfo;
    }

    private <T> T getFutureIgnoreException(Future<T> future) {
        try {
            return (future != null) ? future.get() : null;
        } catch (Exception e) {
            log.warn("error get result from future", e);
            return null;
        }
    }

    /**
     * 获取相关推荐视频列表
     *
     * @param reqProperty
     * @return
     * @throws BizException
     */
    public CompletableFuture<FeedsList> getRecVideoV1(VideoRecListReqProperty reqProperty) {
        String id = reqProperty.getDocid();
        if (StringUtils.isEmpty(id)) {
            log.warn("invalid docid:{}", reqProperty.getDocid());
            CompletableFuture future = new CompletableFuture();
            future.completeExceptionally(new BadRequestRuntimeException("invalid docId:" + reqProperty.getDocid()));
            return future;
        }
        // 兜底算法请求参数处理
        if (reqProperty.getOffset() == 0) {
            reqProperty.setDownTimes(0);
        } else {
            reqProperty.setUpTimes(reqProperty.getRefreshTimes());
        }
        // 8.3 短小切自建 ，相关推荐统一修改为经过算法
        // 暂时注释掉从媒体号取视频作为相关推荐视频列表内容的逻辑，  后续需要可取消注释恢复
//        if ("subv_game".equals(reqProperty.getFromId())) {
            return recommendService.getRelatedRecommend(reqProperty).thenApply(recommendList -> {
                ExtraInfo extraInfo = buildRecommendExtraInfo(recommendList, BizUtils.getRelevantBidLst(youliApiConfig));
                return getFromContentMiddle(reqProperty, recommendList.getResult(), extraInfo);
            });
//        }
//        CompletableFuture<String> mediaNoCf;
//        if (StringUtils.isEmpty(reqProperty.getMediaNo())) {
//            List<String> docIds = new ArrayList<>();
//            docIds.add(id);
//            mediaNoCf = middleVideoResource.getResourceListCf(reqProperty, docIds).thenApply(resourceList -> {
//                if (CollectionUtils.isEmpty(resourceList) || resourceList.get(0) == null) {
//                    log.warn("{} is not exist article", id);
//                    throw new BadRequestRuntimeException("this article is not exist");
//                }
//                return resourceList.get(0).getAuthor().getId();
//            });
//        } else {
//            mediaNoCf = CompletableFuture.completedFuture(reqProperty.getMediaNo());
//        }
//        return mediaNoCf.thenCompose(mediaNo -> {
//            VideoMediaListReqProperty mediaReqProperty = new VideoMediaListReqProperty();
//            mediaReqProperty.setMediaNo(mediaNo);
//            mediaReqProperty.setType("0");
//            if (reqProperty.getDetailStyle() == 3) {
//                // detailStyle=3 2N详情页 仅获取4个相关推荐
//                mediaReqProperty.setNumber(youliApiConfig.getVideoRec2NLimit());
//            } else {
//                mediaReqProperty.setNumber(15);
//            }
//            mediaReqProperty.setAttributeValues(reqProperty.getAttributeValues());
//            mediaReqProperty.setOffset(0);
//            return getMediaList(mediaReqProperty).thenApply(feedsList -> {
//                if (feedsList == null || CollectionUtils.isEmpty(feedsList.getItems())) {
//                    return feedsList;
//                }
//                Iterator<Item> iterator = feedsList.getItems().iterator();
//                while (iterator.hasNext()) {
//                    if (id.equals(iterator.next().getId())) {
//                        iterator.remove();
//                        break;
//                    }
//                }
//                Collections.shuffle(feedsList.getItems());
//                return feedsList;
//            });
//        });
    }

    /**
     * 获取单个视频详情
     *
     * @param articleInfoReqProperty
     * @return
     * @throws BizException
     */
    public Article getArticleInfo(VideoInfoReqProperty articleInfoReqProperty) throws BizException {
        String id = articleInfoReqProperty.getDocid();
        if (StringUtils.isEmpty(id)) {
            log.warn("invalid docid:{}", articleInfoReqProperty.getDocid());
            throw new BadRequestException("invalid docId:" + articleInfoReqProperty.getDocid());
        }

        List<String> docIds = new ArrayList<>();
        docIds.add(articleInfoReqProperty.getDocid());
        FeedsListReqProperty feedsListReqProperty = new FeedsListReqProperty();
        feedsListReqProperty.setSource(articleInfoReqProperty.getSource());
        feedsListReqProperty.setFeedssession(articleInfoReqProperty.getFeedssession());
        feedsListReqProperty.setVersion(articleInfoReqProperty.getVersion());
        feedsListReqProperty.setFromId(articleInfoReqProperty.getFromId());
        feedsListReqProperty.setAttributeValues(articleInfoReqProperty.getAttributeValues());
        List<VideoResource> resourceList = middleVideoResource.getResourceList(feedsListReqProperty, docIds);
        ExtraInfo extraInfo = new ExtraInfo();
        extraInfo.setDpOpenFrom(articleInfoReqProperty.getDpOpenFrom());
        FeedsList feedsList = getFromContentMiddle(feedsListReqProperty, resourceList, extraInfo);
        commentService.setCommentCount(feedsList.getArticles());

        if (feedsList == null || feedsList.getArticles() == null || feedsList.getArticles().size() != 1) {
            log.warn("{} is not exist article", articleInfoReqProperty.getDocid());
            throw new BadRequestException("this article is not exist");
        }

        return feedsList.getArticles().get(0);
    }

    /**
     * 批量获取视频详情
     *
     * @param batchArticleInfoReqProperty
     * @return
     * @throws BizException
     */
    public CompletableFuture<Map<String, Article>> getBatchArticleInfo(BatchArticleInfoReqProperty batchArticleInfoReqProperty) {
        FeedsListReqProperty feedsListReqProperty = new FeedsListReqProperty();
        feedsListReqProperty.setSource(FeedsConstant.TopResource.YOULI);
        feedsListReqProperty.setAttributeValues(batchArticleInfoReqProperty.getAttributeValues());
        feedsListReqProperty.setFeedssession(batchArticleInfoReqProperty.getFeedssession());
        feedsListReqProperty.setFromId(batchArticleInfoReqProperty.getFromId());
        feedsListReqProperty.setVersion(batchArticleInfoReqProperty.getVersion());

        Map<String, Article> result = new LinkedHashMap<>();//保证按照查询列表里docId顺序排序
        List<String> docIds = batchArticleInfoReqProperty.getDocIds();
        CompletableFuture<List<VideoResource>> resourceListCf = middleVideoResource.getResourceListCf(feedsListReqProperty, docIds);
        CompletableFuture<Map<String, Integer>> commentCntCf = commentService.getArticlesCmtCountAsync(docIds);
        return CompletableFuture.allOf(resourceListCf, commentCntCf).thenApply(aVoid -> {
            List<VideoResource> resourceList = getFutureIgnoreException(resourceListCf);
            Map<String, Integer> commentCnt = getFutureIgnoreException(commentCntCf);
            FeedsList feedsList = getFromContentMiddle(feedsListReqProperty, resourceList, null);
            commentService.setArticlesCmtCount(feedsList.getArticles(), commentCnt);
            if (CollectionUtils.isNotEmpty(feedsList.getArticles())) {
                for (Article article : feedsList.getArticles()) {
                    result.put(article.getId(), article);
                }
            }
            return result;
        });
    }

    /**
     * 获取媒体号详情
     *
     * @param reqProperty
     * @return
     * @throws BizException
     */
    public VideoMediaInfo getMediaInfo(VideoMediaInfoReqProperty reqProperty) throws BizException {
        return middleMediaResource.getMediaInfo(reqProperty);
    }

    /**
     * 获取媒体号视频列表
     *
     * @param reqProperty
     * @return
     * @throws BizException
     */
    public CompletableFuture<FeedsList> getMediaList(VideoMediaListReqProperty reqProperty) {
        return middleMediaResource.getMediaResourceList(reqProperty).thenCompose(resourceList -> {
            if (CollectionUtils.isEmpty(resourceList)) {
                return CompletableFuture.completedFuture(new FeedsList());
            }
            FeedsListReqProperty feedsListReqProperty = new FeedsListReqProperty();
            feedsListReqProperty.setSource(FeedsConstant.TopResource.YOULI);
            feedsListReqProperty.setFeedssession(reqProperty.getFeedssession());
            feedsListReqProperty.setVersion(reqProperty.getVersion());
            feedsListReqProperty.setFromId("media");
            FeedsList feedsList = getFromContentMiddle(feedsListReqProperty, resourceList, null);
            List<String> ids = new ArrayList<>(resourceList.size());
            resourceList.forEach(videoResource -> ids.add(videoResource.getId()));
            return commentService.getArticlesCmtCountAsync(ids).handle((commentCnt, throwable) -> {
                commentService.setArticlesCmtCount(feedsList.getArticles(), commentCnt);
                return feedsList;
            });
        });
    }

    private void writeFallbackData(FeedsListReqProperty request, FeedsList feedsList) {
        if (feedsList == null || CollectionUtils.isEmpty(feedsList.getArticles())) {
            return;
        }
        String fallbackDataKey = String.format("video:%s:%s", request.getSource(), request.getFromId());
        try {
            if (feedsList.getArticles().size() > 3) {
                log.info("************ write response data to fallbackDataKey:[{}] ************",
                        fallbackDataKey);
                feedsList.setFallback(Boolean.TRUE);
                FallbackDataUtil.writeFallbackData(fallbackDataKey, JsonTools.toJsonString(feedsList));
                feedsList.setFallback(Boolean.FALSE);
            }
        } catch (Exception e) {
            log.warn("write data to kafka exception: key:{}", fallbackDataKey, e);
        }
    }

    public FeedsList getFromContentMiddle(ListBaseReqProperty feedsListReqProperty, List<? extends VideoResource> resourceList, ExtraInfo extraInfo) {
        FeedsList feedsList = new FeedsList();
        if (MyListUtils.isEmpty(resourceList)) {
            return feedsList;
        }
        int count = 0;
        for (VideoResource videoResource : resourceList) {
            if (videoResource == null || videoResource.getResourceDetail() == null || CollectionUtils.isEmpty(videoResource.getResourceDetail().getResourceDetailList())) {
                log.warn("invalid video resource:{}, resourceList:{}, req:{}", videoResource, resourceList,
                        feedsListReqProperty);
                MonitorUtil.counter(MetricConstant.METERICS_VIDEO_YOULI_FILTER_COUNT, 1);
                continue;
            }
            // 设置item
            Item item = new Item();
            item.setId(videoResource.getId());
            setItem(item);

            // 设置article
            Article article = new Article();
            article.setId(videoResource.getId());
            setArticle(article, videoResource, feedsListReqProperty, extraInfo);
            setRelationInfo(videoResource, article, feedsListReqProperty);
            setExtraInfo(article, extraInfo);
            setFilterWords(article, videoResource);
            // 设置详情页URL
            article.setUrl(buildVideoDetailUrl(feedsListReqProperty, videoResource));
            // 设置评论页URL
            article.setCmtUrl(buildVideoCommentUrl(feedsListReqProperty, videoResource.getId()));
            article.setIssuedReason(ResourceConfig.getRecIssuedReason());
            feedsList.addItem(item);
            feedsList.addArticle(article);
            count++;
        }
        feedsList.setNewsCount(count);
        if (feedsListReqProperty.getOffset() == null || feedsListReqProperty.getOffset() < 1) {
            feedsList.setOffset(count);
        } else {
            feedsList.setOffset(count + feedsListReqProperty.getOffset());
        }
        return feedsList;
    }

    private void setRelationInfo(VideoResource videoResource, Article article, ListBaseReqProperty request) {
        if (request.getVersion() < 52000 || videoResource == null || !(videoResource instanceof Recommend) || article == null) {
            return;
        }
        Recommend recommend = (Recommend) videoResource;
        if (CollectionUtils.isEmpty(recommend.getAlgoRelationInfoList())) {
            return;
        }
        List<Long> appIds = recommend.getAlgoRelationInfoList().stream()
                .filter(info -> info != null && "game".equals(info.getType())).map(info -> NumberUtils.toLong(info.getId()))
                .filter(appId -> appId != 0).limit(youliApiConfig.getGameNum()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(appIds)) {
            return;
        }
        Map<Long, AppDetailInfo> map = null;
        try {
            map = appStoreService.queryAppInfoWithBookStatus(appIds, request, request.getAndroidVersionCode()).get();
            log.debug("appStoreService.queryAppInfoWithBookStatus, request:{}, appIds:{}, map:{}", request, appIds, map);
        } catch (Exception e) {
            log.warn("appStoreService.queryAppInfoWithBookStatus exception, request:{}, appIds:{}", request, appIds, e);
        }
        if (MapUtils.isEmpty(map)) {
            return;
        }
        for (long appId : appIds) {
            AppDetailInfo appBaseDetailDto = map.get(appId);//NOSONAR
            if (appBaseDetailDto == null) {
                continue;
            }
            RelationInfo relationInfo = buildRelationInfo(appBaseDetailDto);
            if (relationInfo == null) {
                continue;
            }
            article.setRelationList(Arrays.asList(relationInfo));
            return;
        }

//        if (CollectionUtils.isEmpty(videoResource.getRelationInfoList())) {
//            return;
//        }
//        if (EDU_LONG_VIDEO_RELATED_TYPE.equals(videoResource.getRelationInfoList().get(0).getRelatedType()) && request.getVersion().intValue() < EDU_LONG_VIDEO_SUPPORT_MIN_VERSION) {
//            return;
//        }
//        Relation relation = videoResource.getRelationInfoList().get(0).getInfo();
//        if (relation == null)
//            return;
//        RelationInfo relationInfo = new RelationInfo();
//        relationInfo.setId(relation.getSid());
//        relationInfo.setEid(relation.getEid());
//        relationInfo.setTitle(relation.getTitle());
//        relationInfo.setSource(FeedsConstant.TopResource.YOULI);
//        relationInfo.setSourceMedia(relation.getSource());
//        relationInfo.setDeeplink(relation.getDeepLinkUrl());
//        relationInfo.setCategory(relation.getChineseType());
//        relationInfo.addImage(relation.getHorizontalImage());
//        relationInfo.setYear(relation.getYear());
//        relationInfo.setArea(relation.getArea());
//        relationInfo.setSubCategory(relation.getCategory());
//        relationInfo.setContentType(RelationTypeConstant.LONG_VIDEO);
//        relationInfo.setStyleType(StyleTypeConstant.LONG_VIDEO);
//        article.setRelationList(Arrays.asList(relationInfo));
    }

    private RelationInfo buildRelationInfo(AppDetailInfo appDto) {
        if (StringUtils.isAnyBlank(appDto.getAppName(), appDto.getPkgName(), appDto.getIconUrl(), appDto.getShortDesc())) {
            log.info("filter app cause by appName、pkgName、iconUrl or shortDesc is empty. appDto={}", appDto);
            return null;
        }
        RelationInfo relationInfo = new RelationInfo();
        relationInfo.setContentType(RelationTypeConstant.APP_INFO);
        relationInfo.setId("");//字段不能空null，填充为""
        relationInfo.setStyleType(StyleTypeConstant.LONG_VIDEO);
        relationInfo.setAppInfo(new AppInfo(appDto, appStoreService.downloadToken(appDto.getPkgName())));
        relationInfo.setIssuedReason("recommend");
        return relationInfo;
    }

    private void setExtraInfo(Article article, ExtraInfo extraInfo) {
        if (extraInfo == null) {
            return;
        }
        if (extraInfo.getTaggedAuthor() != null) {
            article.setTaggedAuthor(StringUtils.defaultString(extraInfo.getTaggedAuthor().get(article.getId())));
        }
        if (extraInfo.getTaggedTitle() != null) {
            article.setTaggedTitle(StringUtils.defaultString(extraInfo.getTaggedTitle().get(article.getId())));
        }
        article.setStatisticsid(StringUtils.defaultString(extraInfo.getRid(), ""));
        article.setPageId(StringUtils.defaultString(extraInfo.getBidlst(), ""));
        //自建算法请求id放入到每个article对象中
        article.setAiRid(StringUtils.defaultString(extraInfo.getRid(), ""));
    }

    private void setItem(Item item) {
        if (item == null) {
            return;
        }
        item.setMap("articles");
    }

    private void setArticle(Article article, VideoResource videoResource, ListBaseReqProperty feedsListReqProperty, ExtraInfo extraInfo) {
        article.setTitle(StringUtils.defaultIfEmpty(videoResource.getArtificialTitle(), videoResource.getTitle()));
        article.setSource(FeedsConstant.TopResource.YOULI);
        article.setSourceMedia(videoResource.getVendor());
        Author author = videoResource.getAuthor();
        article.setSourceName(author.getName());
        article.setStyleType(videoResource.getContentType() == 2 ? StyleTypeConstant.VIDEO : StyleTypeConstant.SMALL_VIDEO);
        //5.0版本后不再设置描述默认值
        if ((StringUtils.isEmpty(videoResource.getSummary()) || videoResource.getSummary().equals(article.getTitle())) &&
                feedsListReqProperty.getVersion() < 50000) {
            article.setSummary(ResourceConfig.getDefaultSummary());
        } else {
            article.setSummary(videoResource.getSummary());
        }
        VideoCountInfo count = videoResource.getCount();
        if (count == null) {
            count = new VideoCountInfo();
            log.error("videoResource no count:{}", videoResource);
        }
        article.setDislikeCnt((int) count.getDislike());
        article.setPublishTime(videoResource.getPublishTime());
        article.setBeHotTime((long) videoResource.getPublishTime()); //以发布时间作为审核通过时间
        article.setPageId(feedsListReqProperty.getFromId());
        article.setContentType(videoResource.getContentType() == 2 ? 2 : 5);
        article.setCmtCnt(ResourceConfig.isCmtShowNum() ? (int) count.getComment() : 0);
        article.setShareCnt((int) count.getShare());
        article.setCmtUrl(""); // 评论URL
        article.setCmtNumShowType(ResourceConfig.getCmtType());  // 传给客户端评论列表h5/native通知标识
        Random random = new Random(article.getId().hashCode());
        int randomPlayCnt =
                random.nextInt(ResourceConfig.getMaxPlaycnt()) + ResourceConfig.getMinPlaycnt() + (int) count.getPlay();
        int randomLikeCnt =
                random.nextInt(ResourceConfig.getMaxLikecnt()) + ResourceConfig.getMinLikecnt() + (int) count.getLike();
        article.setViewCnt(randomPlayCnt);
        article.setLikeCnt(randomLikeCnt % (randomPlayCnt + 1));
        article.setStatisticsName(FeedsConstant.TopResource.YOULI);
        article.setOutId(videoResource.getThirdPartyId());
        article.setCategory(Arrays.asList(ArrayUtils.nullToEmpty(videoResource.getCategory())));

        //视频满足出媒体号的条件才出，不满足则不出
        if (!StringUtils.isEmpty(author.getName()) && !StringUtils.isEmpty(author.getId()) && videoResource.getPublishTime() != 0) {
            Medium medium = new Medium();
            medium.setName(author.getName());
            if (!StringUtils.isEmpty(author.getAvatar())) {
                medium.setAvatar(author.getAvatar());
            } else {
                medium.setAvatar(ResourceConfig.getDefaultAuthorAvatar());
            }
            medium.setMediaNo(author.getId());
            medium.setMediaSource(FeedsConstant.TopResource.YOULI);
            medium.setFollowStatus(true);
            article.setMedium(medium);
            article.setSourceName(medium.getName());
        } else {
            article.setSourceName(ResourceConfig.getMediaName(article.getId()));
        }

        Video video = new Video();
        setVideo(article, video, videoResource);
        List<Video> videos = new ArrayList<>();
        videos.add(video);
        article.setVideos(videos);
        String openFrom = StringUtils.EMPTY;
        if(extraInfo != null && StringUtils.isNotEmpty(extraInfo.getDpOpenFrom())){
            openFrom = extraInfo.getDpOpenFrom();
        }
        article.setDeeplink(String.format(ResourceConfig.getDeepLinkUrl(),
                videoResource.getContentType() == 2 ? "short_video" : "small_video",
                article.getId(), article.getSource(), MyHttpUtils.urlEncoder(video.getUrl()), openFrom));
    }


    private void setVideo(Article article, Video video, VideoResource videoResource) {
        if (videoResource == null || video == null || videoResource.getResourceDetail() == null) {
            return;
        }
        VideoDetailInfo videoDetailInfo = videoResource.getResourceDetail();
        List<VideoInfo> videoInfos = videoDetailInfo.getResourceDetailList();
        if (MyListUtils.isEmpty(videoInfos)) {
            return;
        }
        //播放地址按照清晰度从低到高排列
        Collections.sort(videoInfos, QualityComparable.getInstance());
        //循环处理播放地址
        for (VideoInfo videoInfo : videoInfos) {
            VideoUrl videoUrl = new VideoUrl();
            Double size = videoInfo.getSize() != 0 ? videoInfo.getSize() :
                    (long) (0.085 * videoResource.getResourceDetail().getDuration() * 1024 * 1024);
            videoUrl.setSize(size.intValue());
            videoUrl.setQuality(videoInfo.getQuality());
            //videoUrl.setUrl(getCdnPlayUrl(videoInfo.getUrl(), videoResource, videoInfo.getQuality()));
            videoUrl.setUrl(videoInfo.getUrl());
            videoUrl.setFormat(videoInfo.getFormat());
            video.addVideoUrl(videoUrl);
        }
        //默认为最高清晰度播放地址
        video.setUrl(video.getUrls().get(video.getUrls().size() - 1).getUrl());
        video.setLength((int) videoDetailInfo.getDuration());
        String img = handleImgUrl(videoResource);
        if (StringUtils.isEmpty(img)) {
            log.warn("img empty,videoResource:{}", videoResource);
        }
//        img = getCdnImgUrl(img);
        video.setImage(img);
        video.setViewCnt(article.getViewCnt());
    }

//    private String getCdnPlayUrl(String url, VideoResource videoResource, int quality) {
//        String[] ossAccessList = FeedsAccessConfig.getOssAccessList();
//        for (String ossAccess : ossAccessList) {
//            if (!url.startsWith(ossAccess)) {
//                continue;
//            }
//            return FeedsAccessConfig.getFeedsAccess() + "/video/toutiaoVideo302?source=" + FeedsConstant.TopResource.YOULI +
//                    "&videoId="
//                    + videoResource.getId() + "&authorId=" + videoResource.getAuthor().getId()
//                    + "&quality=" + quality + "&timestamp=" + StringUtils.deleteWhitespace(SimpleDateFormatHolder.getSimpleDateFormat().format(new Date()))
//                    + "&keyword=" + url.substring(ossAccess.length());
//        }
//        return url;
//    }
//
//    private String getCdnImgUrl(String url) {
//        String[] ossAccessImgList = FeedsAccessConfig.getOssAccessImgList();
//        for (String ossAccessImg : ossAccessImgList) {
//            if (!url.startsWith(ossAccessImg)) {
//                continue;
//            }
//            return FeedsAccessConfig.getCdnAccessImg() + url.substring(ossAccessImg.length());
//        }
//        return url;
//    }

    private String buildVideoDetailUrl(ListBaseReqProperty feedsListReqProperty, VideoResource resource) {
        String url = org.apache.commons.lang.StringUtils.replace(ResourceConfig.getVideoDetailUrl(), "${docId}", resource.getId());
        // 拼getArticleInfo接口需要的参数；source和docid是必须的
        StringBuilder sb = StringBuilderHolder.getStringBuilder();
        sb.append(url);
        sb.append("?__t=");
        sb.append(System.currentTimeMillis() / 1000);
        sb.append("&__docid__=");
        sb.append(resource.getId());
        sb.append("&feedssession=");
        sb.append(feedsListReqProperty.getFeedssession());
        sb.append("&__fromId__=");
        sb.append(feedsListReqProperty.getFromId());
        sb.append("&enterId=");
        sb.append(feedsListReqProperty.getEnterId());
        sb.append("&__source__=");
        sb.append(FeedsConstant.TopResource.YOULI);
        sb.append("&statisticsid=");
        sb.append(feedsListReqProperty.getStatisticsid());
        sb.append("&version=");
        sb.append(feedsListReqProperty.getVersion());
        sb.append("&mediaNo=");
        sb.append(resource.getAuthor().getId());
        return sb.toString();
    }


    private String buildVideoCommentUrl(ListBaseReqProperty feedsListReqProperty, String docId) {
        String url = StringUtils.replace(ResourceConfig.getVideoCommentUrl(), "${docId}", docId);
        // 拼getArticleInfo接口需要的参数；source和docid是必须的
        StringBuilder sb = StringBuilderHolder.getStringBuilder();
        sb.append(url);
        sb.append("?__t=");
        sb.append(System.currentTimeMillis() / 1000);
        sb.append("&__docid__=");
        sb.append(docId);
        sb.append("&feedssession=");
        sb.append(feedsListReqProperty.getFeedssession());
        sb.append("&__fromId__=");
        sb.append(feedsListReqProperty.getFromId());
        sb.append("&__source__=");
        sb.append(FeedsConstant.TopResource.YOULI);
        sb.append("&statisticsid=");
        sb.append(feedsListReqProperty.getStatisticsid());
        sb.append("&version=");
        sb.append(feedsListReqProperty.getVersion());
        sb.append(ResourceConfig.getCmtAppendUrl());
        return sb.toString();
    }

    private void setFilterWords(Article article, VideoResource videoResource) {
        if (article == null || videoResource == null || !(videoResource instanceof Recommend)) {
            return;
        }
        Recommend recommend = (Recommend) videoResource;
        List<FilterWord> filterWords = recommend.getDislikeTags();
        if (CollectionUtils.isEmpty(filterWords)) {
            return;
        }
        List<ReasonObj> reasonObjs = new ArrayList<>();
        for (FilterWord filterWord : filterWords) {
            ReasonObj reasonObj = new ReasonObj();
            reasonObj.setId(filterWord.getId());
            reasonObj.setName(filterWord.getName());
            reasonObj.setSelected(false);
            reasonObjs.add(reasonObj);
        }
        article.setFilterWords(reasonObjs);
    }

    private String handleImgUrl(VideoResource videoResource) {
        if (CollectionUtils.isNotEmpty(videoResource.getSmartPicList()) && videoResource.getSmartPicList().get(0) != null
                && StringUtils.isNotEmpty(videoResource.getSmartPicList().get(0).getPicUrl())) {
            return videoResource.getSmartPicList().get(0).getPicUrl();
        }

        if (CollectionUtils.isNotEmpty(videoResource.getArtificialViewPics()) && StringUtils.isNotEmpty(videoResource.getArtificialViewPics().get(0).getPicUrl())) {
            return videoResource.getArtificialViewPics().get(0).getPicUrl();
        }
        String originUrl = "";
        if (CollectionUtils.isNotEmpty(videoResource.getCoverList())) {
            originUrl = videoResource.getCoverList().get(0).getPicUrl();
        }
        return originUrl;
    }

    public CompletableFuture<VideoFeedsListRt> getPondVideos(GetPondVideosReqProperty nRequest) {
        return middlePondResource.getPondVideos(nRequest).thenCompose(pondVideos -> {
            FeedsListReqProperty feedsListReqProperty = new FeedsListReqProperty();
            feedsListReqProperty.setSource(FeedsConstant.TopResource.YOULI);
            feedsListReqProperty.setFeedssession(nRequest.getFeedssession());
            feedsListReqProperty.setVersion(nRequest.getVersion());
            feedsListReqProperty.setAttributeValues(nRequest.getAttributeValues());
            return handlePondVideos(feedsListReqProperty, pondVideos, false);
        });
    }

}
