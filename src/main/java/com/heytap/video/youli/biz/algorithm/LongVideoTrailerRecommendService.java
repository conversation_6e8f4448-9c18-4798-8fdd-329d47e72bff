package com.heytap.video.youli.biz.algorithm;

import com.alibaba.fastjson.JSON;
import com.heytap.longvideo.client.arrange.LvTrailerOperationRpcApi;
import com.heytap.longvideo.client.arrange.entity.LvTrailerOperationItem;
import com.heytap.longvideo.client.arrange.model.response.TrailerOperationBO;
import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.StandardAlbumRpcApi;
import com.heytap.longvideo.client.media.StandardTrailerRpcApi;
import com.heytap.longvideo.client.media.StandardVideoRpcApi;
import com.heytap.longvideo.client.media.VirtualProgramRelationRpcApi;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.entity.StandardTrailer;
import com.heytap.longvideo.client.media.entity.StandardVideo;
import com.heytap.longvideo.client.media.entity.oppomedia.MisVirtualProgramRelation;
import com.heytap.longvideo.client.media.query.PageRequest;
import com.heytap.longvideo.client.media.query.TrailerExample;
import com.heytap.video.youli.cache.AlgorithmPoolCache;
import com.heytap.video.youli.config.LongVideoConfig;
import com.heytap.video.youli.config.LongVideoTrailerRecommendConfig;
import com.heytap.video.youli.model.algorithm.LvtRecommendAlgorithmData;
import com.heytap.video.youli.model.algorithm.LvtRecommendAlgorithmResponse;
import com.heytap.video.youli.utils.FutureUtil;
import com.oppo.browser.common.app.lib.cookie.Cookie;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import com.oppo.browser.strategy.model.AttributeValues;
import com.oppo.browser.video.common.pubobj.resource.FeedsListReqProperty;
import com.oppo.cpc.video.framework.lib.programsource.SourceVersionService;
import esa.rpc.common.context.RpcContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.heytap.video.youli.cache.AlgorithmPoolCache.FALLBACK_TYPE_HTTP_ERROR;

/**
 * <AUTHOR> Yanping
 * @date 2022/10/12 15:54
 */
@Slf4j
@Service
public class LongVideoTrailerRecommendService {

    @Autowired
    private HttpDataChannel httpDataChannel;

    @Autowired
    private LongVideoTrailerRecommendConfig longVideoTrailerRecommendConfig;

    @Autowired
    @Qualifier("standardAlbumRpcApi")
    private StandardAlbumRpcApi albumRpcApi;

    @Autowired
    @Qualifier("standardVideoRpcApi")
    private StandardVideoRpcApi videoRpcApi;

    @Autowired
    @Qualifier("virtualProgramRelationRpcApi")
    private VirtualProgramRelationRpcApi virtualProgramRelationRpcApi;

    @Autowired
    @Qualifier("lvTrailerOperationRpcApi")
    private LvTrailerOperationRpcApi lvTrailerOperationRpcApi;

    @Autowired
    @Qualifier("standardTrailerRpcApi")
    private StandardTrailerRpcApi standardTrailerRpcApi;

    @Autowired
    private LongVideoConfig longVideoConfig;

    @Autowired
    private SourceVersionService sourceVersionService;

    @Autowired
    private AlgorithmPoolCache algorithmPoolCache;

    public CompletableFuture<LvtRecommendAlgorithmResponse> getRecommendAlgorithmDatas(FeedsListReqProperty reqProperty) {
        //周边视频推荐流	 B1655276899003	 场景
        if (StringUtils.isNotEmpty(reqProperty.getSid()) && StringUtils.isNotEmpty(reqProperty.getVid())) {
            if (reqProperty.getImmersionTrailerScene() != null && reqProperty.getImmersionTrailerScene() == 1 &&
                    StringUtils.isNotBlank(reqProperty.getTrailerOperationCode()) && reqProperty.getOriginalChannel() != null) {
                //详情页周边视频进入沉浸式，将周边视频列表通过getList接口下发
                return getDetailPageTrailerData(reqProperty);
            }
            return requestAlgorithm(reqProperty, longVideoConfig.getAlgorithmTypeOne());
        } else if (StringUtils.isNotEmpty(reqProperty.getPoolCode())) { //内容池周边视频推荐 B1655276899006 场景
            return requestAlgorithm(reqProperty, longVideoConfig.getAlgorithmTypeTwo());
        } else {
            log.warn("sid or vid or poolCode is null, reqProperty:{}", reqProperty);
            return CompletableFuture.completedFuture(null);
        }
    }


    /**
     * 获取周边视频数据，保持和详情页下发的周边视频数据一致
     * @param reqProperty
     * @return
     */
    private CompletableFuture<LvtRecommendAlgorithmResponse> getDetailPageTrailerData(FeedsListReqProperty reqProperty) {

        //1、获取人工渠道配置的周边视频数据.
        if (reqProperty.getOriginalChannel() == 0) {
            log.debug("operationChannel, sid:{}, trailerOperationCode:{}", reqProperty.getSid(), reqProperty.getTrailerOperationCode());
            HashSet<String> hashSet = new HashSet<>();
            hashSet.add(reqProperty.getTrailerOperationCode());
            return queryTrailerOperationData(hashSet).handle(((vidList, e) ->
                    handle2CommonResponse(reqProperty.getSid(), reqProperty.getInsertVid(), vidList)));
        }

        //2、详情页只下发了媒资的周边视频数据(此时详情页下发的周边视频模块code以default开头)
        if (reqProperty.getTrailerOperationCode().contains("default_")) {
            log.debug("default originalChannel, sid:{}, trailerOperationCode:{}", reqProperty.getSid(), reqProperty.getTrailerOperationCode());
            return queryStandardTrailersBy(reqProperty.getSid()).handle((vidList, e)->
                    handle2CommonResponse(reqProperty.getSid(), reqProperty.getInsertVid(), vidList));
        }

        //3、详情页同时下发了人工配置和媒资的周边视频数据，此时需要将媒资周边视频模块的数据进行去重
        return handleRepeatedMediaDta(reqProperty);
    }

    private CompletableFuture<LvtRecommendAlgorithmResponse> handleRepeatedMediaDta(FeedsListReqProperty reqProperty) {
        //详情页同时下发了人工配置和媒资的周边视频数据，此时需要将媒资周边视频模块的数据进行去重
        HashSet<String> hashSet = new HashSet<>();
        if (StringUtils.isNotBlank(reqProperty.getOtherTrailerOperationCodes())) {
            String[] codesArray = reqProperty.getOtherTrailerOperationCodes().split(",");
            hashSet.addAll(new HashSet<>(Arrays.asList(codesArray)));
        }
        //分别查询原始媒资周边视频列表和人工配置的周边视频列表
        CompletableFuture<List<String>> mediaDataCF = queryStandardTrailersBy(reqProperty.getSid());
        CompletableFuture<List<String>> operationDataCF = queryTrailerOperationData(hashSet);

        return CompletableFuture.allOf(mediaDataCF, operationDataCF).handle((aVoid, e)->{
            if (e != null) {
                log.error("mediaData and standardTrailerList is empty, sid:{}, OtherTrailerOperationCodes:{}", reqProperty.getSid(), reqProperty.getOtherTrailerOperationCodes());
                return null;
            }
            List<String> mediaVidList = FutureUtil.getFutureIgnoreException(mediaDataCF);
            List<String> operationVidList = FutureUtil.getFutureIgnoreException(operationDataCF);
            if (CollectionUtils.isEmpty(mediaVidList)) {
                log.error("mediaData standardTrailerList is empty, sid:{}", reqProperty.getSid());
                return null;
            }

            if (CollectionUtils.isEmpty(operationVidList)) {
                return handle2CommonResponse(reqProperty.getSid(), reqProperty.getInsertVid(), mediaVidList);
            }
            //原始媒资周边视频去重
            mediaVidList.removeIf(operationVidList::contains);
            return handle2CommonResponse(reqProperty.getSid(), reqProperty.getInsertVid(), mediaVidList);
        });
    }


    private CompletableFuture<List<String>> queryTrailerOperationData(HashSet<String> hashSet) {
        try {
            RpcContext.getConsumerContext().setAttachment("ROUTING_DB", "api");
            return lvTrailerOperationRpcApi.findLvTrailerOperationSetByCodes(hashSet)
                    .handle((rpcResult, e)->{
                        if (e != null || rpcResult == null || rpcResult.getCode() != 0 || CollectionUtils.isEmpty(rpcResult.getData())) {
                            log.error("lvTrailerOperationRpcApi.findLvTrailerOperationSetByCodes error , codes:{}, error msg:", JSON.toJSONString(hashSet), e);
                            return Collections.emptyList();
                        }
                        // 使用iterator()方法获取HashSet的迭代器
                        Iterator<TrailerOperationBO> iterator = rpcResult.getData().iterator();
                        //结果
                        List<LvTrailerOperationItem> lvTrailerOperationItemList = new ArrayList<>();
                        while (iterator.hasNext()) {
                            TrailerOperationBO trailerOperationBO = iterator.next();
                            if (trailerOperationBO != null && CollectionUtils.isNotEmpty(trailerOperationBO.getItems())) {
                                lvTrailerOperationItemList.addAll(trailerOperationBO.getItems());
                            }
                        }
                        return lvTrailerOperationItemList.stream().map(LvTrailerOperationItem::getVid).collect(Collectors.toList());
                    });
        }catch (Exception e) {
            log.error("lvTrailerOperationRpcApi.findLvTrailerOperationSetByCodes error , codes:{}, error msg:", JSON.toJSONString(hashSet), e);
            return CompletableFuture.completedFuture(Collections.emptyList());
        }

    }

    /**
     * 批量查询周边视频列表
     * @param sid
     * @return
     */
    private CompletableFuture<List<String>> queryStandardTrailersBy(String sid) {
        TrailerExample trailerExample = new TrailerExample();
        trailerExample.setStatus(1);
        trailerExample.setSid(sid);
        PageRequest pageRequest = new PageRequest(0, 35);
        try {
            return standardTrailerRpcApi.queryBy(trailerExample, pageRequest).handle((rpcResult, e)->{
                if (e != null || rpcResult == null || rpcResult.getCode() != 0 || CollectionUtils.isEmpty(rpcResult.getData())) {
                    log.error("standardTrailerRpcApi.queryBy error , sid:{}, ", sid, e);
                    return Collections.emptyList();
                }
                return rpcResult.getData().stream().map(StandardTrailer::getVid).collect(Collectors.toList());
            });
        }catch (Exception e) {
            log.error("queryStandardTrailersBy error, error msg:", e);
            return CompletableFuture.completedFuture(Collections.emptyList());
        }

    }

    private LvtRecommendAlgorithmResponse handle2CommonResponse(String sid, String insertVid, List<String> vidList) {
        //构建统一返回
        List<LvtRecommendAlgorithmData> data = new ArrayList<>();

        //当列表页配置了起播类型为周边视频的节目时，并且用户点击的周边视频所在模块为第一个周边视频模块，请求参数的insertVid为该周边视频
        //这时将其插入到周边视频列表的第一个位置
        if (StringUtils.isNotBlank(insertVid) && CollectionUtils.isNotEmpty(vidList) && !vidList.contains(insertVid)) {
            vidList.add(0, insertVid);
        }

        if (CollectionUtils.isNotEmpty(vidList)) {
            log.debug("standardTrailerList is not empty, sid:{}", sid);
            for (String videoId : vidList) {
                LvtRecommendAlgorithmData lvtRecommendAlgorithmData = new LvtRecommendAlgorithmData();
                lvtRecommendAlgorithmData.setId(sid);
                lvtRecommendAlgorithmData.setVideoId(videoId);
                data.add(lvtRecommendAlgorithmData);
            }
        }
        return buildCommonResponse(data);
    }

    private LvtRecommendAlgorithmResponse buildCommonResponse(List<LvtRecommendAlgorithmData> data) {

        LvtRecommendAlgorithmResponse response = new LvtRecommendAlgorithmResponse();
        response.setStatus(0);
        response.setData(data);
        response.setNum(data.size());
        return response;
    }

    public CompletableFuture<LvtRecommendAlgorithmResponse> requestAlgorithm(FeedsListReqProperty reqProperty, Integer algorithmType) {
        Map<String, String> params = buildBaseParams(reqProperty, algorithmType);
        addAlgorithmTypeSpecificParams(params, reqProperty, algorithmType);
        addCommonRequestParams(params, reqProperty);

        String algorithmUrl = determineAlgorithmUrl(algorithmType);
        addVersionSpecificParams(params, reqProperty, algorithmType);

        return executeAlgorithmRequest(algorithmUrl, params, reqProperty, algorithmType);
    }

    private Map<String, String> buildBaseParams(FeedsListReqProperty reqProperty, Integer algorithmType) {
        Map<String, String> params = new HashMap<>();
        params.put("r_dv", reqProperty.getAttributeValues().getPhone());
        params.put("route", longVideoTrailerRecommendConfig.getRecommendAlgorithmRoute());
        params.put("cid", longVideoTrailerRecommendConfig.getRecommendAlgorithmCid());
        params.put("bidlst", longVideoTrailerRecommendConfig.getBidList().get(algorithmType));
        return params;
    }

    private void addAlgorithmTypeSpecificParams(Map<String, String> params, FeedsListReqProperty reqProperty, Integer algorithmType) {
        if (longVideoConfig.getAlgorithmTypeOne().equals(algorithmType)) {
            params.put("r_channel_id", longVideoTrailerRecommendConfig.getRecommendAlgorithmChannelId());
            params.put("docId", reqProperty.getSid());
            params.put("video_id", reqProperty.getVid());
        } else if (isContentPoolAlgorithmType(algorithmType)) {
            params.put("r_content_pool_id", reqProperty.getPoolCode());
        }
    }

    private boolean isContentPoolAlgorithmType(Integer algorithmType) {
        return longVideoConfig.getAlgorithmTypeTwo().equals(algorithmType) ||
               longVideoConfig.getAlgorithmTypeUGC().equals(algorithmType) ||
               longVideoConfig.getAlgorithmTypeFXS().equals(algorithmType);
    }

    private void addCommonRequestParams(Map<String, String> params, FeedsListReqProperty reqProperty) {
        int num = calculateVideoNum(reqProperty);
        params.put("num", String.valueOf(num));

        long buuid = extractBuuid(reqProperty);
        params.put("r_buuid", String.valueOf(buuid));
        params.put("r_page", String.valueOf(reqProperty.getRefreshTimes()));
        params.put("r_channel_name", String.valueOf(reqProperty.getLvChannelName()));
        params.put("r_page_id", String.valueOf(reqProperty.getPageCode()));
    }

    private int calculateVideoNum(FeedsListReqProperty reqProperty) {
        return (reqProperty.isFetchMoreVideosSwitch() && reqProperty.getContentCount() != null)
                ? reqProperty.getContentCount() : reqProperty.getLimit();
    }

    private long extractBuuid(FeedsListReqProperty reqProperty) {
        AttributeValues attributeValues = reqProperty.getAttributeValues();
        long buuid = attributeValues.getBuuid();

        if (buuid == 0) {
            Cookie cookie = reqProperty.getScookieIgnoreException();
            if (isCookieBuuidValid(cookie)) {
                buuid = cookie.getInfo().getBuuid();
            }
        }
        return buuid;
    }

    private boolean isCookieBuuidValid(Cookie cookie) {
        return cookie != null &&
               cookie.getInfo() != null &&
               cookie.getInfo().getBuuid() != null;
    }

    private String determineAlgorithmUrl(Integer algorithmType) {
        if (longVideoConfig.getAlgorithmTypeUGC().equals(algorithmType)) {
            return longVideoTrailerRecommendConfig.getUgcAlgorithmUrl();
        }
        return longVideoTrailerRecommendConfig.getRecommendAlgorithmUrl();
    }

    private void addVersionSpecificParams(Map<String, String> params, FeedsListReqProperty reqProperty, Integer algorithmType) {
        if (shouldAddSourceList(reqProperty, algorithmType)) {
            params.put("r_source_list", sourceVersionService.getSourceStrByVersion(reqProperty.getVersion()));
        }
    }

    private boolean shouldAddSourceList(FeedsListReqProperty reqProperty, Integer algorithmType) {
        return reqProperty.getVersion() != null &&
               (longVideoConfig.getAlgorithmTypeOne().equals(algorithmType) ||
                longVideoConfig.getAlgorithmTypeTwo().equals(algorithmType));
    }

    private CompletableFuture<LvtRecommendAlgorithmResponse> executeAlgorithmRequest(
            String algorithmUrl, Map<String, String> params, FeedsListReqProperty reqProperty, Integer algorithmType) {

        CompletableFuture<LvtRecommendAlgorithmResponse> future;
        try {
            future = httpDataChannel.asyncGetForObject(algorithmUrl, LvtRecommendAlgorithmResponse.class,
                    params, longVideoTrailerRecommendConfig.getRecommendAlgorithmTimeout());
        } catch (Exception e) {
            log.error("get lvtRecommendAlgorithmResponse error, the params:{}, the exception:{}", params, e);
            return CompletableFuture.completedFuture(
                    algorithmPoolCache.getAlgorithmCacheResponse(reqProperty, algorithmType, FALLBACK_TYPE_HTTP_ERROR));
        }

        return future.handle((response, e) -> handleAlgorithmResponse(response, e, params, reqProperty, algorithmType));
    }

    private LvtRecommendAlgorithmResponse handleAlgorithmResponse(
            LvtRecommendAlgorithmResponse response, Throwable e, Map<String, String> params,
            FeedsListReqProperty reqProperty, Integer algorithmType) {

        if (isResponseInvalid(response, e)) {
            log.error("get lvtRecommendAlgorithmResponse error, the params:{}, the response:{}", params, response, e);
            return algorithmPoolCache.getAlgorithmCacheResponse(reqProperty, algorithmType, FALLBACK_TYPE_HTTP_ERROR);
        }

        if (CollectionUtils.isEmpty(response.getData())) {
            log.error("getRecommendAlgorithmSids return null, the params:{}", params);
            return algorithmPoolCache.getAlgorithmCacheResponse(reqProperty, algorithmType, FALLBACK_TYPE_HTTP_ERROR);
        }

        return response;
    }

    private boolean isResponseInvalid(LvtRecommendAlgorithmResponse response, Throwable e) {
        return e != null ||
               response == null ||
               response.getStatus() == null ||
               response.getStatus() != 0;
    }

    /**
     * 获取剧头
     *
     * @param sidSet
     * @return
     */
    public CompletableFuture<RpcResult<Map<String/* sid */, StandardAlbum>>> getAlbumsBySids(Set<String> sidSet) {
        return albumRpcApi.getBySidsFilterInvalid(sidSet.stream().collect(Collectors.toList()));
    }

    public CompletableFuture<RpcResult<Map<String/*vid*/, StandardVideo>>> getVideosByVids(Map<String, String> vidSidMap) {
        return videoRpcApi.getByVids(vidSidMap);
    }


    /**
     * 批量查询虚拟sid
     *
     * @param sidSet
     * @return
     */
    public CompletableFuture<RpcResult<Map<String/* sid */, MisVirtualProgramRelation>>> queryVirtualSidsBySids(Set<String> sidSet) {
        return virtualProgramRelationRpcApi.queryBySids(sidSet.stream().collect(Collectors.toList()));
    }


}