package com.heytap.video.youli.biz.algorithm;

import com.heytap.video.fallback.model.FallbackHttpReqParam;
import com.heytap.video.fallback.model.FallbackResponse;
import com.heytap.video.fallback.util.FallbackHttpChannelUtils;
import com.heytap.video.youli.config.YouliApiConfig;
import com.heytap.video.youli.constant.Bidlst;
import com.heytap.video.youli.model.algorithm.RecommendList;
import com.heytap.video.youli.utils.BizUtils;
import com.heytap.video.youli.utils.UrlUtil;
import com.oppo.browser.common.app.lib.cookie.Cookie;
import com.oppo.browser.common.app.lib.encryption.JsonTools;
import com.oppo.browser.common.app.lib.utils.Env;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import com.oppo.browser.common.pubobj.feeds.runtimeexceptions.InvalidDataRuntimeException;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import com.oppo.browser.dfoob.channel.HttpDataChannelException;
import com.oppo.browser.strategy.model.AttributeValues;
import com.oppo.browser.video.common.app.lib.strategy.IPLocationStrategyResult;
import com.oppo.browser.video.common.pubobj.constant.RecTypeConstant;
import com.oppo.browser.video.common.pubobj.resource.FeedsListReqProperty;
import com.oppo.browser.video.common.pubobj.resource.ListBaseReqProperty;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.FeedsList;
import com.oppo.trace.core.TraceThreadLocal;
import com.oppo.trace.core.scope.TraceScope;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import static com.heytap.video.youli.constant.GlobalConstant.APPID;
import static com.heytap.video.youli.constant.GlobalConstant.R_ACTION;
import static com.heytap.video.youli.constant.GlobalConstant.R_PAGE;
import static com.heytap.video.youli.constant.GlobalConstant.TRACEID;

@Slf4j
@Service
public class RecommendService {
    protected static final String VIDEO = "video";
    
    @Autowired
    private YouliApiConfig youliApiConfig;

    @Autowired
    private HttpDataChannel httpDataChannel;

    /**
     * 获取信息流推荐列表(带降级)
     *
     * @param reqProperty
     * @return
     * @throws BizException
     */
    public CompletableFuture<FallbackResponse<RecommendList, FeedsList>> getFeedsRecommend(FeedsListReqProperty reqProperty) {
        //是否为推荐频道
        boolean isRecChannel = VIDEO.equals(reqProperty.getFromId());
        String fallbackKey = String.format("video:%s:%s", reqProperty.getSource(), reqProperty.getFromId());
        String url = isRecChannel ? youliApiConfig.getMainRecommendUrl() : youliApiConfig.getChannelRecommendUrl();
        FallbackHttpReqParam<RecommendList, FeedsList> param = new FallbackHttpReqParam(url, fallbackKey, youliApiConfig.getRecommendSocketTimeout(), UrlUtil.getContentCommonHeaders(), JsonTools.toJsonString(getReqParams(reqProperty)), RecommendList.class, FeedsList.class);

        String interfaceName = isRecChannel ? "mainRec" : "channelRec";
        return FallbackHttpChannelUtils.executeHttpPostAsync(interfaceName, param);
    }

    /**
     * 获取相关推荐列表
     *
     * @param reqProperty
     * @return
     * @throws BizException
     */
    public CompletableFuture<RecommendList> getRelatedRecommend(ListBaseReqProperty reqProperty) {
        Map<String, Object> reqParams = getRecReqParams(reqProperty);
        String url =  youliApiConfig.getDetailRelevantUrl();
        CompletableFuture<RecommendList> recommendListCf;
        try {
            recommendListCf = httpDataChannel.asyncPostForObject(url, JsonTools.toJsonString(reqParams), RecommendList.class, null, UrlUtil.getContentCommonHeaders(), youliApiConfig.getRelevantSocketTimeout());
        } catch (HttpDataChannelException e) {
            log.warn("getRelatedRecommend HttpDataChannelException, url:{}", url, e);
            recommendListCf = CompletableFuture.completedFuture(null);
        }
        return recommendListCf.handle((recommendList, throwable) -> {
            if ((throwable != null) || (recommendList == null) || (recommendList.getCode() != 0) || CollectionUtils.isEmpty(recommendList.getResult())) {
                log.warn("recommend_api :[url:{}][rt:{}]", url, recommendList, throwable);
                throw new InvalidDataRuntimeException("recommend result:" +
                        (recommendList != null ? (recommendList.getMessage() + "[code:" + recommendList.getCode() + "]") : null));
            }

            return recommendList;
        });
    }

    private Map<String, Object> getReqParams(FeedsListReqProperty reqProperty) {
        Map<String, Object> params = new HashMap<>();
        //非必传，固定值，bjhtonlinerec(测试环境必须传递，用来路由)
        if("dev".equals(Env.getEnv()) || "test".equals(Env.getEnv())){
            params.put("route", "bjhtonlinerec");
        }

        //必传，业务ID
        params.put(APPID, 2);

        //必传，请求时间，毫秒
        params.put("time", System.currentTimeMillis());

        //必传，请求id或表示一某刷，用来跟踪整个响应过程。（推荐那这为tranceid）
        if(StringUtils.isNotEmpty(reqProperty.getRequestId())){
            params.put(TRACEID, reqProperty.getRequestId());
        }else if(TraceThreadLocal.getScope() != null && TraceThreadLocal.getScope().getAttachments() != null && StringUtils.isNotEmpty(TraceThreadLocal.getScope().getAttachments().get(TraceScope.ATTACHMENT_KEY.REQUEST_ID))){
            params.put(TRACEID, TraceThreadLocal.getScope().getAttachments().get(TraceScope.ATTACHMENT_KEY.REQUEST_ID));
        }else{
            params.put(TRACEID, UUID.randomUUID().toString().replace("-", ""));
        }

        //必传，业务id， 如：feed/video/xxx
        params.put("cid", VIDEO);

        //必传，请求推荐的条数，最大值为60
        params.put("num", youliApiConfig.getEachRefreshNum());

        //必传，推荐栏ID或推荐位置ID，由推荐系统定义分配；不同的推荐栏ID会使用不同的推荐策略。
        params.put("bidlst", getBidLstByFromId(reqProperty.getFromId()));

        AttributeValues attributeValues = reqProperty.getAttributeValues();
        //buuid（必传）和imei（非必传）
        appendCommonParams(params, attributeValues, reqProperty);
        if(RecTypeConstant.H5.equals(reqProperty.getRecType())){
            appendH5Params(params, reqProperty);
        }

        //必传，用于标识当前的频道Id是什么（个性化推荐、相关推荐、作者推荐有独特的频道Id）
        params.put("r_channel_id", getChannelIdByFromId(reqProperty.getFromId()));

        //非必传，当前的频道类型
        params.put("r_channel_type", StringUtils.defaultIfEmpty(reqProperty.getType(), VIDEO));

        // upTimes和downTimes在video-list-rest中的com.heytap.video.list.handler.Handler#addPullParams方法转化而来
        if (reqProperty.getOffset() == 0) {
            //必传，当前刷新行为 0 ：下拉 ，1： 翻页（上划），2：首页按钮触发刷新
            params.put(R_ACTION, 0);

            //必传，第几刷：2 （从0开始 为第一刷）
            params.put(R_PAGE, reqProperty.getDownTimes());
        } else {
            params.put(R_ACTION, 1);
            params.put(R_PAGE, reqProperty.getUpTimes());
        }

        //必传，刷新方式（(0：被动刷新，1：主动刷新）
        params.put("r_refresh", 1);

        //非必传，经度
        //params.put("r_lng", reqProperty.getLongitude());

        //非必传，纬度
        //params.put("r_lat", reqProperty.getLatitude());

        //非必传，城市 country:{国家};province:{省名};city:{城市名}（推荐使用）
        if (!StringUtils.isEmpty(reqProperty.getArea())) {
            IPLocationStrategyResult location = JsonTools.toMap(reqProperty.getArea(), IPLocationStrategyResult.class);
            if (location != null) {
                params.put("r_area", String.format("country:%s;province:%s;city:%s", StringUtils.defaultString(location.getCountry()), StringUtils.defaultString(location.getProvince()), StringUtils.defaultString(location.getCity())));
            }
        }

        //非必传，上网环境；wifi，3g，4g，5g (推荐接口为r_network)
        if(StringUtils.isNotEmpty(attributeValues.getNetwork())){
            params.put("network", attributeValues.getNetwork());
        }

        //非必传，机型；r9s
        if(StringUtils.isNotEmpty(attributeValues.getPhone())){
            params.put("r_devtype", attributeValues.getPhone());
        }

        //非必传，是否启用个性化推荐（1、启用，0、关闭），默认启用
        params.put("personalRec", 1);

        //非必传，用户名
        if(reqProperty.getScookieIgnoreException() != null && reqProperty.getScookieIgnoreException().getInfo() != null && StringUtils.isNotEmpty(reqProperty.getScookieIgnoreException().getInfo().getUn())){
            params.put("r_username", reqProperty.getScookieIgnoreException().getInfo().getUn());
        }

        //非必传，api版本（ 服务端版本比如：28，29）
        //params.put("r_feeds_sdk_version", "");

        //非必传，客户端版本 （为了兼容，负一屏映射为r_negscreen_version，信息流映射为r_browser_version）
        if(StringUtils.isNotEmpty(attributeValues.getClientFullBrowserVersion())){
            params.put("r_client_version", attributeValues.getClientFullBrowserVersion());
        }

        //非必传，debug等级（算法debug时需要传）
        //params.put("debugLevel", 1);

        //非必传，客户端ip地址   (推荐接口为r_ip)
        if(StringUtils.isNotEmpty(attributeValues.getIp())){
            params.put("ip", attributeValues.getIp());
        }

        //非必传，是否需要一带N信息
        params.put("needRelation", false);

        //非必传，图片转化格式： 1:webp（仅picUrl支持返回）
        params.put("picFormatType", 1);

        //非必传，abtest透传值
        //params.put("attachment", "");
        //非必传，push透传参数
        //params.put("pushParams", "");
        //非必传，算法扩展字段（新增字段可通过该字段给算法透传，不再解析新字段）
        //示例：sids=24%2CrecMediaBatchFollow&smallvideo=true&r_daypage=1&supportMixedVideo=0
        StringBuilder recommendExtValue = new StringBuilder();
        // push沉浸式落地页上滑
        // 内容id传递
        if ("push_detail_2N".equals(reqProperty.getSpageID())) {
            recommendExtValue.append("iid=")
                    .append(reqProperty.getPushDocId())
                    .append("&");
        } else if (StringUtils.isNotBlank(reqProperty.getRootGid())){
            recommendExtValue.append("iid=")
                    .append(reqProperty.getRootGid())
                    .append("&");
        }
        // 上一个页面的id透传
        recommendExtValue.append("parFromId=")
                    .append(reqProperty.getSpageID());
        if(reqProperty.getVersion() >= 52000){
            recommendExtValue.append("&gameNum=").append(youliApiConfig.getGameNum());
        }
        params.put("recommendExt", recommendExtValue.toString());
        //非必传，用于push的内容请求沉浸式视频，push的内容，点击返回后，请求一刷内容，可能是个性推荐，也可以是频道推荐
        //params.put("r_push_doc_id", "");
        //非必传，是否首页true/false， 但是这个只仅仅用于业务首页判定，不能用于是否为个性化推荐首页。因为首页不一定就是个性化推荐。
        params.put("homepage", false);

        return params;
    }

    /**
     * 构建相关推荐请求参数
     *
     * <p>该方法是重构后的主方法，通过调用多个子方法来构建完整的推荐请求参数。
     * 重构前该方法的圈复杂度为24，重构后降低到6以下。</p>
     *
     * <h3>重构策略：</h3>
     * <ul>
     *   <li>方法分解（Extract Method）- 将大方法分解为多个小方法</li>
     *   <li>逻辑分层 - 按功能职责将参数构建分为四个层次</li>
     *   <li>条件提取 - 将复杂条件判断提取为独立方法</li>
     *   <li>通用方法提取 - 提取重复的参数添加逻辑</li>
     * </ul>
     *
     * <h3>参数构建层次：</h3>
     * <ol>
     *   <li>基础参数 - 环境、业务ID、时间、追踪ID等</li>
     *   <li>推荐参数 - 推荐数量、频道信息、刷新行为等</li>
     *   <li>设备用户参数 - 设备信息、用户信息、地理位置等</li>
     *   <li>扩展参数 - 业务扩展字段、固定配置等</li>
     * </ol>
     *
     * @param reqProperty 请求属性对象，包含所有请求相关信息
     * @return 构建完成的推荐请求参数Map
     */
    private Map<String, Object> getRecReqParams(ListBaseReqProperty reqProperty) {
        Map<String, Object> params = new HashMap<>();

        // 构建基础参数
        buildRecBaseParams(params, reqProperty);

        // 构建推荐相关参数
        buildRecRecommendParams(params, reqProperty);

        // 构建设备和用户相关参数
        buildRecDeviceAndUserParams(params, reqProperty);

        // 构建扩展参数
        buildRecExtensionParams(params, reqProperty);

        return params;
    }

    /**
     * 构建基础请求参数
     *
     * <p>构建推荐请求的基础参数，包括环境相关、业务标识、时间戳和追踪ID等。
     * 这些参数是所有推荐请求都必须包含的核心参数。</p>
     *
     * @param params 参数Map，用于存储构建的参数
     * @param reqProperty 请求属性对象
     */
    private void buildRecBaseParams(Map<String, Object> params, ListBaseReqProperty reqProperty) {
        // 路由参数（测试环境）
        addRouteParamIfNeeded(params);

        // 基础必传参数
        params.put(APPID, 2);
        params.put("time", System.currentTimeMillis());
        params.put("cid", VIDEO);

        // 请求追踪ID
        addTraceId(params, reqProperty);
    }

    /**
     * 根据环境添加路由参数
     *
     * <p>在测试环境下添加路由参数，用于请求路由到正确的服务实例。
     * 生产环境不需要此参数。</p>
     *
     * @param params 参数Map
     */
    private void addRouteParamIfNeeded(Map<String, Object> params) {
        if (isTestEnvironment()) {
            params.put("route", "bjhtonlinerec");
        }
    }

    /**
     * 判断当前是否为测试环境
     *
     * <p>通过环境变量判断当前运行环境，测试环境包括 "env" 和 "test"。
     * 该方法将原来的复杂条件判断提取为独立方法，降低了圈复杂度。</p>
     *
     * @return true 如果是测试环境，false 如果是生产环境
     */
    private boolean isTestEnvironment() {
        String env = Env.getEnv();
        return "env".equals(env) || "test".equals(env);
    }

    /**
     * 添加请求追踪ID
     *
     * <p>从请求属性中提取或生成追踪ID，用于链路追踪和问题排查。</p>
     *
     * @param params 参数Map
     * @param reqProperty 请求属性对象
     */
    private void addTraceId(Map<String, Object> params, ListBaseReqProperty reqProperty) {
        String traceId = extractTraceId(reqProperty);
        params.put(TRACEID, traceId);
    }

    /**
     * 提取请求追踪ID
     *
     * <p>按优先级从不同来源提取追踪ID：</p>
     * <ol>
     *   <li>请求属性中的 requestId</li>
     *   <li>链路追踪上下文中的 REQUEST_ID</li>
     *   <li>生成新的 UUID（去除连字符）</li>
     * </ol>
     *
     * <p>该方法将原来复杂的 if-else 逻辑分层处理，提高了代码的可读性。</p>
     *
     * @param reqProperty 请求属性对象
     * @return 追踪ID字符串
     */
    private String extractTraceId(ListBaseReqProperty reqProperty) {
        if (StringUtils.isNotEmpty(reqProperty.getRequestId())) {
            return reqProperty.getRequestId();
        }

        String traceFromScope = getTraceIdFromScope();
        if (StringUtils.isNotEmpty(traceFromScope)) {
            return traceFromScope;
        }

        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 从链路追踪上下文获取追踪ID
     *
     * <p>尝试从 TraceThreadLocal 中获取当前请求的追踪ID。
     * 如果上下文无效则返回 null。</p>
     *
     * @return 追踪ID字符串，如果获取失败则返回 null
     */
    private String getTraceIdFromScope() {
        if (isTraceScopeValid()) {
            return TraceThreadLocal.getScope().getAttachments().get(TraceScope.ATTACHMENT_KEY.REQUEST_ID);
        }
        return null;
    }

    /**
     * 验证链路追踪上下文是否有效
     *
     * <p>检查 TraceScope 和其 attachments 是否都不为 null。
     * 该方法将复杂的 && 条件判断提取为独立方法。</p>
     *
     * @return true 如果上下文有效，false 如果无效
     */
    private boolean isTraceScopeValid() {
        return TraceThreadLocal.getScope() != null &&
               TraceThreadLocal.getScope().getAttachments() != null;
    }

    private void buildRecRecommendParams(Map<String, Object> params, ListBaseReqProperty reqProperty) {
        // 推荐数量
        Integer num = determineRecommendNum(reqProperty);
        params.put("num", num);

        // 推荐栏ID
        params.put("bidlst", BizUtils.getRelevantBidLst(youliApiConfig));

        // 频道相关参数
        params.put("r_channel_id", reqProperty.getFromId());
        params.put("r_channel_type", VIDEO);

        // 刷新行为参数
        addRefreshActionParams(params, reqProperty);

        // 刷新方式
        params.put("r_refresh", 1);
    }

    private Integer determineRecommendNum(ListBaseReqProperty reqProperty) {
        if (reqProperty.getDetailStyle() == 3) {
            // detailStyle=3 2N详情页 仅获取4个相关推荐
            return youliApiConfig.getVideoRec2NLimit();
        }
        return youliApiConfig.getVideoRecLimit();
    }

    private void addRefreshActionParams(Map<String, Object> params, ListBaseReqProperty reqProperty) {
        if (reqProperty.getOffset() == 0) {
            params.put(R_ACTION, 0);
            params.put(R_PAGE, reqProperty.getDownTimes());
        } else {
            params.put(R_ACTION, 1);
            params.put(R_PAGE, reqProperty.getUpTimes());
        }
    }

    private void buildRecDeviceAndUserParams(Map<String, Object> params, ListBaseReqProperty reqProperty) {
        AttributeValues attributeValues = reqProperty.getAttributeValues();

        // 用户标识参数
        appendCommonParams(params, attributeValues, reqProperty);
        handleH5ParamsIfNeeded(params, reqProperty);

        // 地理位置参数
        addLocationParams(params, reqProperty);

        // 设备相关参数
        addDeviceParams(params, attributeValues);

        // 用户相关参数
        addUserParams(params, reqProperty);
    }

    private void handleH5ParamsIfNeeded(Map<String, Object> params, ListBaseReqProperty reqProperty) {
        if (RecTypeConstant.H5.equals(reqProperty.getRecType())) {
            appendH5Params(params, reqProperty);
        }
    }

    private void addLocationParams(Map<String, Object> params, ListBaseReqProperty reqProperty) {
        if (StringUtils.isNotEmpty(reqProperty.getArea())) {
            IPLocationStrategyResult location = JsonTools.toMap(reqProperty.getArea(), IPLocationStrategyResult.class);
            if (location != null) {
                String areaStr = String.format("country:%s;province:%s;city:%s",
                    StringUtils.defaultString(location.getCountry()),
                    StringUtils.defaultString(location.getProvince()),
                    StringUtils.defaultString(location.getCity()));
                params.put("r_area", areaStr);
            }
        }
    }

    private void addDeviceParams(Map<String, Object> params, AttributeValues attributeValues) {
        addParamIfNotEmpty(params, "network", attributeValues.getNetwork());
        addParamIfNotEmpty(params, "r_devtype", attributeValues.getPhone());
        addParamIfNotEmpty(params, "r_client_version", attributeValues.getClientFullBrowserVersion());
        addParamIfNotEmpty(params, "ip", attributeValues.getIp());
    }

    private void addUserParams(Map<String, Object> params, ListBaseReqProperty reqProperty) {
        params.put("personalRec", 1);

        String username = extractUsername(reqProperty);
        if (StringUtils.isNotEmpty(username)) {
            params.put("r_username", username);
        }
    }

    private String extractUsername(ListBaseReqProperty reqProperty) {
        if (isCookieUsernameValid(reqProperty)) {
            return reqProperty.getScookieIgnoreException().getInfo().getUn();
        }
        return null;
    }

    private boolean isCookieUsernameValid(ListBaseReqProperty reqProperty) {
        return reqProperty.getScookieIgnoreException() != null &&
               reqProperty.getScookieIgnoreException().getInfo() != null &&
               StringUtils.isNotEmpty(reqProperty.getScookieIgnoreException().getInfo().getUn());
    }

    private void buildRecExtensionParams(Map<String, Object> params, ListBaseReqProperty reqProperty) {
        // 固定参数
        params.put("needRelation", false);
        params.put("picFormatType", 1);
        params.put("r_used", "0");
        params.put("r_relatedId", reqProperty.getDocid());
        params.put("r_cardtype", "VIDEO");

        // 推荐扩展参数
        addRecommendExtParam(params, reqProperty);

        // 媒体号参数
        addParamIfNotEmpty(params, "authorId", reqProperty.getMediaNo());
    }

    private void addRecommendExtParam(Map<String, Object> params, ListBaseReqProperty reqProperty) {
        String recommendExt = "push_detail_2N".equals(reqProperty.getPageID())
            ? "parFromId=push_click"
            : "parFromId=" + reqProperty.getSpageID();
        params.put("recommendExt", recommendExt);
    }

    /**
     * 条件添加参数到Map中
     *
     * <p>只有当值不为空时才将参数添加到Map中，避免添加无效的空值参数。
     * 这是一个通用的工具方法，消除了代码中多处重复的条件判断。</p>
     *
     * <h3>重构效果：</h3>
     * <ul>
     *   <li>消除了多处重复的 StringUtils.isNotEmpty() 判断</li>
     *   <li>提供了统一的参数添加逻辑</li>
     *   <li>降低了主方法的圈复杂度</li>
     *   <li>提高了代码的一致性和可维护性</li>
     * </ul>
     *
     * @param params 参数Map
     * @param key 参数键
     * @param value 参数值，只有非空时才会被添加
     */
    private void addParamIfNotEmpty(Map<String, Object> params, String key, String value) {
        if (StringUtils.isNotEmpty(value)) {
            params.put(key, value);
        }
    }

    private void appendCommonParams(Map<String, Object> params, AttributeValues attributeValues, ListBaseReqProperty reqProperty) {
        long buuid = attributeValues.getBuuid();
        Cookie cookie = reqProperty.getScookieIgnoreException();
        if(buuid == 0 && cookie != null && cookie.getInfo() != null && cookie.getInfo().getBuuid() != null){
            buuid = cookie.getInfo().getBuuid();
        }
        params.put("buuid", buuid);// 用户唯一标识的ID：buuid
        params.put("imei", StringUtils.defaultString(attributeValues.getImei()));// imei号
//        params.put("ouid", StringUtils.defaultString(attributeValues.getOuid()));// openId的ouid
//        params.put("udid", StringUtils.defaultString(attributeValues.getUdid()));// openId的udid
//        params.put("duid", StringUtils.defaultString(attributeValues.getDuid()));// openId的duid
    }

    private void appendH5Params(Map<String, Object> params, ListBaseReqProperty reqProperty) {
        long buuid;
        Cookie cookie = reqProperty.getScookieIgnoreException();
        if(cookie != null && cookie.getInfo() != null && cookie.getInfo().getBuuid() != null){
            buuid = cookie.getInfo().getBuuid();
        } else{
            buuid = new SecureRandom().nextInt(Integer.MAX_VALUE);
            buuid = buuid == 0 ? 1 : buuid;
        }
        params.put("buuid", buuid);
        params.put("buuidtype", "random");
    }

    /**
     * 从算法频道映射map中获取对应fromId的算法频道值
     * 若map中无对应fromId，则返回fromId
     * @param fromId
     * @return
     */
    private String getChannelIdByFromId(String fromId) {
        if (StringUtils.isBlank(fromId) || MapUtils.isEmpty(youliApiConfig.getRecChannelIdMap())) {
            return fromId;
        }
        return youliApiConfig.getRecChannelIdMap().getOrDefault(fromId, fromId);
    }

    private String getBidLstByFromId(String fromId) {
        if (StringUtils.isBlank(fromId) || MapUtils.isEmpty(youliApiConfig.getRecBidLstMap())) {
            return fromId;
        }
        return youliApiConfig.getRecBidLstMap().getOrDefault(fromId, Bidlst.CHANNEL);
    }

}

