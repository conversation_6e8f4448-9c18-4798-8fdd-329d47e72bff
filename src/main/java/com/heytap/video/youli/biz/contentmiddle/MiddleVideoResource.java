package com.heytap.video.youli.biz.contentmiddle;

import com.heytap.video.fallback.model.FallbackHttpReqParam;
import com.heytap.video.fallback.model.FallbackResponse;
import com.heytap.video.fallback.util.FallbackHttpChannelUtils;
import com.heytap.video.youli.config.YouliApiConfig;
import com.heytap.video.youli.metric.MetricConstant;
import com.heytap.video.youli.model.content.ResourceResult;
import com.heytap.video.youli.model.content.VideoResource;
import com.heytap.video.youli.utils.UrlUtil;
import com.oppo.browser.app.framework.utils.MyHttpUtils;
import com.oppo.browser.common.app.lib.encryption.JsonTools;
import com.oppo.browser.common.app.lib.http.Request;
import com.oppo.browser.common.pubobj.feeds.exceptions.BizException;
import com.oppo.browser.common.pubobj.feeds.exceptions.InvalidDataException;
import com.oppo.browser.common.pubobj.feeds.runtimeexceptions.InvalidDataRuntimeException;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import com.oppo.browser.dfoob.channel.HttpDataChannelException;
import com.oppo.browser.strategy.model.AttributeValues;
import com.oppo.browser.video.common.pubobj.resource.FeedsListReqProperty;
import com.oppo.browser.video.common.pubobj.resource.ListBaseReqProperty;
import com.oppo.browser.video.common.utils.UrlUtils;
import com.oppo.cpc.video.framework.lib.metrics.MonitorUtil;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MiddleVideoResource {
    @Autowired
    private YouliApiConfig youliApiConfig;

    @Autowired
    private HttpDataChannel httpDataChannel;

    public List<VideoResource> getResourceList(FeedsListReqProperty reqProperty, List<String> docIds) throws BizException {
        if(CollectionUtils.isEmpty(docIds)){
            return Collections.emptyList();
        }
        Map<String, Object> params = getRequestMiddleParam(reqProperty.getAttributeValues(), docIds);
        HttpPost httpPost = new HttpPost(youliApiConfig.getVideoResourceUrl());
        Request.addHeader(httpPost, UrlUtil.getContentCommonHeaders());
        String json = MyHttpUtils.execute(httpPost, params, RequestConfig.custom().setConnectTimeout(100)
                .setConnectionRequestTimeout(200).setSocketTimeout(youliApiConfig.getVideoResourceTimeOut()).build());

        ResourceResult batchResource = StringUtils.isNotEmpty(json) ? JsonTools.toMap(json, ResourceResult.class) : null;

        MonitorUtil.counter(MetricConstant.METERICS_VIDEO_YOULI_API_RET, 1, MetricConstant.TAG_API,
                UrlUtils.getUrlPath(youliApiConfig.getVideoResourceUrl()), MetricConstant.TAG_RET, batchResource == null ? "-1" : batchResource.getCode());

        if ((batchResource == null) || (batchResource.getCode() != 0) ||
                batchResource.getData() == null || CollectionUtils.isEmpty(batchResource.getData().getResourceList())) {
            if(log.isWarnEnabled()){
                log.warn("middle_batchResource_api: [url:{}][param:{}][header:{}][rt:{}]",
                        youliApiConfig.getVideoResourceUrl(), params, httpPost.getAllHeaders(), json);
            }
            MonitorUtil.counter(MetricConstant.METERICS_VIDEO_YOULI_API_DIFF, 1, MetricConstant.TAG_API, UrlUtils.getUrlPath(youliApiConfig.getVideoResourceUrl()));
            throw new InvalidDataException("resource result:" +
                    (batchResource != null ? (batchResource.getMsg() + "[code:" + batchResource.getCode() + "]") : null));
        }
        return batchResource.getData().getResourceList();
    }

    public CompletableFuture<List<VideoResource>> getResourceListCf(ListBaseReqProperty reqProperty, List<String> docIds) {
        if(CollectionUtils.isEmpty(docIds)){
            return CompletableFuture.completedFuture(Collections.emptyList());
        }
        String param = JsonTools.toJsonString(getRequestMiddleParam(reqProperty.getAttributeValues(), docIds));
        Map<String, String> headers = UrlUtil.getContentCommonHeaders();
        CompletableFuture<ResourceResult> resourceResultCf;
        try {
            resourceResultCf = httpDataChannel.asyncPostForObject(youliApiConfig.getVideoResourceUrl(), param, ResourceResult.class, null, headers, youliApiConfig.getVideoResourceTimeOut());
        } catch (HttpDataChannelException e) {
            log.warn("getResourceList HttpDataChannelException, [url:{}][param:{}][header:{}]", youliApiConfig.getVideoResourceUrl(), param, headers, e);
            resourceResultCf = CompletableFuture.completedFuture(null);
        }
        return resourceResultCf.handle((batchResource, throwable) -> {
            if ((throwable != null) || (batchResource == null) || (batchResource.getCode() != 0) ||
                    batchResource.getData() == null || CollectionUtils.isEmpty(batchResource.getData().getResourceList())) {
                if(log.isWarnEnabled()){
                    log.warn("middle_batchResource_api: [url:{}][param:{}][header:{}][rt:{}]",
                            youliApiConfig.getVideoResourceUrl(), param, headers, batchResource, throwable);
                }
                MonitorUtil.counter(MetricConstant.METERICS_VIDEO_YOULI_API_DIFF,
                        docIds.size(), MetricConstant.TAG_API, UrlUtils.getUrlPath(youliApiConfig.getVideoResourceUrl()));
                throw new InvalidDataRuntimeException("resource result:" +
                        (batchResource != null ? (batchResource.getMsg() + "[code:" + batchResource.getCode() + "]") : null));
            }
            List<VideoResource> resourceList = batchResource.getData().getResourceList();
            MonitorUtil.counter(MetricConstant.METERICS_VIDEO_YOULI_API_DIFF,
                    docIds.size() - resourceList.size(), MetricConstant.TAG_API, UrlUtils.getUrlPath(youliApiConfig.getVideoResourceUrl()));
            return resourceList;
        });
    }

    public CompletableFuture<FallbackResponse> getResourceListWithFallback(FeedsListReqProperty reqProperty, List<String> docIds){
        if(CollectionUtils.isEmpty(docIds)){
            return CompletableFuture.completedFuture(null);
        }
        Map<String, Object> params = getRequestMiddleParam(reqProperty.getAttributeValues(), docIds);
        HttpPost httpPost = new HttpPost(youliApiConfig.getVideoResourceUrl());
        Request.addHeader(httpPost, UrlUtil.getContentCommonHeaders());
        String fallbackKey = String.format("video:%s:%s", reqProperty.getSource(), reqProperty.getFromId());
        FallbackHttpReqParam reqParam =  new FallbackHttpReqParam(youliApiConfig.getVideoResourceUrl(), fallbackKey,
                youliApiConfig.getVideoResourceTimeOut(), UrlUtil.getContentCommonHeaders(), JsonTools.toJsonString(params));
        return FallbackHttpChannelUtils.executeHttpPostAsync("midResource", reqParam);
    }

    private Map<String, Object> getRequestMiddleParam(AttributeValues attributeValues, List<String> idList) {
        Map<String, Object> params = new HashMap<>();
        if (attributeValues != null) {
            params.put("ip", attributeValues.getIp());
            params.put("network", attributeValues.getNetwork());
            if(attributeValues.getBuuid() != 0){
                params.put("buuid", String.valueOf(attributeValues.getBuuid()));
            }
        }
        params.put("appId", 2);
        params.put("idList", idList);
        params.put("saveMode", 0);
        params.put("needRelation", Boolean.TRUE);
        return params;
    }

}
