package com.heytap.video.youli.biz;

import com.heytap.longvideo.client.common.RpcResult;
import com.heytap.longvideo.client.media.MediaCommonServiceRpcApi;
import com.heytap.longvideo.client.media.StandardAlbumRpcApi;
import com.heytap.longvideo.client.media.UgcStandardAuthorRpcApi;
import com.heytap.longvideo.client.media.UgcStandardVideoRpcApi;
import com.heytap.longvideo.client.media.entity.StandardAlbum;
import com.heytap.longvideo.client.media.entity.StandardVideo;
import com.heytap.longvideo.client.media.entity.UgcStandardVideo;
import com.heytap.longvideo.client.media.entity.YstPlayUrlBO;
import com.heytap.longvideo.client.media.entity.oppomedia.MisVirtualProgramRelation;
import com.heytap.longvideo.client.media.enums.DefinitionLevelEnum;
import com.heytap.longvideo.client.media.enums.SourceEnum;
import com.heytap.video.youli.biz.algorithm.LongVideoTrailerRecommendService;
import com.heytap.video.youli.biz.longvideo.LongVideoCommentService;
import com.heytap.video.youli.config.LongVideoConfig;
import com.heytap.video.youli.config.LongVideoTrailerRecommendConfig;
import com.heytap.video.youli.model.algorithm.LvtRecommendAlgorithmData;
import com.heytap.video.youli.model.algorithm.LvtRecommendAlgorithmResponse;

import static com.heytap.video.youli.utils.FutureUtil.getFutureIgnoreException;

import com.heytap.video.youli.service.sourcefilter.FunshionLongVideoAndWeidiouFilterService;
import com.heytap.video.youli.utils.FutureUtil;
import com.heytap.video.youli.utils.LvProgramTypeUtils;
import com.oppo.browser.common.app.lib.utils.JsonUtil;
import com.oppo.browser.video.common.pubobj.constant.FeedsItemEnum;
import com.oppo.browser.video.common.pubobj.resource.FeedsListReqProperty;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.FeedsList;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.Item;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.LongVideo;
import com.oppo.browser.video.common.pubobj.resultObj.longvideo.VideoPlayDetail;
import com.oppo.browser.video.common.pubobj.resultObj.news.VideoFeedsListRt;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> ZhangXu
 * @description
 * @date 2022-10-21 12:56
 */
@Slf4j
@Service
public class LongVideoResource {
    @Autowired
    private LongVideoTrailerRecommendConfig longVideoTrailerRecommendConfig;

    @Autowired
    private LongVideoCommentService longVideoCommentService;

    @Autowired
    private LongVideoTrailerRecommendService lvtRecommendService;

    @Autowired
    private LongVideoConfig longVideoConfig;

    @Autowired
    private FunshionLongVideoAndWeidiouFilterService funshionLongVideoAndWeidiouFilterService;

    @Autowired
    @Qualifier("ugcStandardVideoRpcApi")
    private UgcStandardVideoRpcApi ugcStandardVideoRpcApi;

    @Autowired
    @Qualifier("ugcStandardAuthorRpcApi")
    private UgcStandardAuthorRpcApi ugcStandardAuthorRpcApi;

    @Autowired
    @Qualifier("standardAlbumRpcApi")
    private StandardAlbumRpcApi albumRpcApi;

    private final MediaCommonServiceRpcApi mediaCommonServiceRpcApi;

    /**
     * 片花负反馈
     */
    private static final List<String> FLOW_TRAILER_DISLIKE_REASON = new ArrayList<String>() {
        {
            add("不感兴趣");
            add("看过了");
            add("重复推荐");
        }
    };

    public LongVideoResource(@Qualifier("mediaCommonServiceRpcApi") MediaCommonServiceRpcApi mediaCommonServiceRpcApi) {
        this.mediaCommonServiceRpcApi = mediaCommonServiceRpcApi;
    }

    /**
     * 根据长视频vid，组装长视频详情
     *
     * @param reqProperty
     * @param response
     * @return
     */
    public CompletableFuture<VideoFeedsListRt> handleResponse(FeedsListReqProperty reqProperty, LvtRecommendAlgorithmResponse response) {
        VideoFeedsListRt rt = new VideoFeedsListRt();
        if (response == null || CollectionUtils.isEmpty(response.getData())) {
            log.warn("empty lvtRecommendAlgorithmResponse, reqProperty:{}, response:{}", reqProperty, response);
            rt.setRet(1400);
            rt.setMsg("empty FeedsRecommendResponse");
            return CompletableFuture.completedFuture(rt);
        }
        List<LvtRecommendAlgorithmData> recommendDatas = response.getData();

        //加上入口视频的sid和vid信息，统一查询，详情页进沉浸式时，不将入口视频放在第一位，它本身已经在视频列表中了
        if (reqProperty.getRefreshTimes() == 0 && reqProperty.getImmersionTrailerScene() == null) {
            LvtRecommendAlgorithmData firstLongVideoData = new LvtRecommendAlgorithmData();
            firstLongVideoData.setId(reqProperty.getSid());
            firstLongVideoData.setVideoId(reqProperty.getVid());
            //放在第一个位置
            recommendDatas.add(0, firstLongVideoData);
        }

        Set<String> sidSet = new HashSet<>();
        Map<String, String> vidSidMap = new HashMap<>();
        //过滤算法返回结果中可能为空的sid/vid
        recommendDatas.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getId()) && StringUtils.isNotEmpty(data.getVideoId()))
                .forEach(data -> {
                    vidSidMap.put(data.getVideoId(), data.getId());
                    sidSet.add(data.getId());
                });

        //获取剧头数据, sid-album
        CompletableFuture<RpcResult<Map<String, StandardAlbum>>> albumMapFuture = lvtRecommendService.getAlbumsBySids(sidSet);
        //获取视频数据, vid-sid
        CompletableFuture<RpcResult<Map<String/*vid*/, StandardVideo>>> videoMapFuture = lvtRecommendService.getVideosByVids(vidSidMap);
        //获取虚拟sid数据
        CompletableFuture<RpcResult<Map<String/* sid */, MisVirtualProgramRelation>>> virtualProgeamRelMapFuture = lvtRecommendService.queryVirtualSidsBySids(sidSet);

        return CompletableFuture.allOf(albumMapFuture, videoMapFuture, virtualProgeamRelMapFuture).handleAsync((res, e) -> {

            RpcResult<Map<String, StandardAlbum>> albumMapResult = getFutureIgnoreException(albumMapFuture);
            Map<String, StandardAlbum> standardAlbumMap = new HashMap<>();
            if (albumMapResult != null && albumMapResult.getCode() == 0 && albumMapResult.getData() != null) {
                standardAlbumMap.putAll(albumMapResult.getData());
            }

            RpcResult<Map<String, StandardVideo>> videoMapResult = getFutureIgnoreException(videoMapFuture);
            Map<String, StandardVideo> standardVideoMap = new HashMap<>();
            if (videoMapResult != null && videoMapResult.getCode() == 0 && videoMapResult.getData() != null) {
                standardVideoMap.putAll(videoMapResult.getData());
            }

            RpcResult<Map<String, MisVirtualProgramRelation>> vSidMapResult = getFutureIgnoreException(virtualProgeamRelMapFuture);
            Map<String, MisVirtualProgramRelation> vSidMap = new HashMap<>();
            if (vSidMapResult != null && vSidMapResult.getCode() == 0 && vSidMapResult.getData() != null) {
                vSidMap.putAll(vSidMapResult.getData());
            }

            return handleFeedsListResponse(standardAlbumMap, standardVideoMap, vSidMap, recommendDatas, reqProperty.getVersion());
        }).thenCompose(feedsList -> {
            if (feedsList == null) {
                rt.setRet(1400);
                rt.setMsg("feedList empty");

                return CompletableFuture.completedFuture(rt);
            }

            String[] virtualSidList = feedsList.getLongVideos().stream().map(longVideo -> longVideo.getVirtualSid()).distinct().toArray(String[]::new);
            //批量查询评论数据并且组装参数
            return longVideoCommentService.getCommentCountAsync(StringUtils.joinWith(",", virtualSidList)).thenCompose(ret -> {
                if (MapUtils.isNotEmpty(ret)) {
                    feedsList.getLongVideos().forEach(longVideo -> longVideo.setCmtCnt(ret.get(longVideo.getVirtualSid())));
                }
                rt.setRet(0);
                rt.setData(feedsList);

                return CompletableFuture.completedFuture(rt);
            });
        });
    }

    /**
     * 根据算法UGC返回，组装UGC视频详情
     *
     * @param reqProperty
     * @param response
     * @return
     */
    public CompletableFuture<VideoFeedsListRt> handleUgc(FeedsListReqProperty reqProperty, LvtRecommendAlgorithmResponse response) {
        VideoFeedsListRt rt = new VideoFeedsListRt();
        if (response == null || CollectionUtils.isEmpty(response.getData())) {
            log.warn("empty lvtRecommendAlgorithmResponse, reqProperty:{}, response:{}", reqProperty, response);
            rt.setRet(1400);
            rt.setMsg("empty FeedsRecommendResponse");
            return CompletableFuture.completedFuture(rt);
        }
        List<LvtRecommendAlgorithmData> recommendDatas = response.getData();

        //加上入口视频的sid和vid信息，统一查询
        if (reqProperty.getRefreshTimes() == 0) {
            LvtRecommendAlgorithmData firstLongVideoData = new LvtRecommendAlgorithmData();
            firstLongVideoData.setId(reqProperty.getSid());
            firstLongVideoData.setVideoId(reqProperty.getVid());
            //放在第一个位置
            recommendDatas.add(0, firstLongVideoData);
        }

        Set<String> sidSet = new HashSet<>();
        Map<String, String> vidSidMap = new HashMap<>();
        //过滤算法返回结果中可能为空的sid/vid
        recommendDatas.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getId()) && StringUtils.isNotEmpty(data.getVideoId()))
                .forEach(data -> {
                    vidSidMap.put(data.getVideoId(), data.getId());
                    sidSet.add(data.getId());
                });

        //获取视频数据, vid-sid
        CompletableFuture<RpcResult<Map<String/*vid*/, UgcStandardVideo>>> videoMapFuture = ugcStandardVideoRpcApi.getByVidList(vidSidMap);

        return videoMapFuture.handleAsync((res, e) -> {

            RpcResult<Map<String, UgcStandardVideo>> videoMapResult = getFutureIgnoreException(videoMapFuture);
            Map<String, UgcStandardVideo> standardVideoMap = new HashMap<>();
            if (videoMapResult != null && videoMapResult.getCode() == 0 && videoMapResult.getData() != null) {
                standardVideoMap.putAll(videoMapResult.getData());
            }

            return handleUgcFeedsListResponse(standardVideoMap, recommendDatas, false);
        }).thenCompose(feedsList -> {
            if (feedsList == null) {
                rt.setRet(1400);
                rt.setMsg("feedList empty");

                return CompletableFuture.completedFuture(rt);
            }

            String[] virtualSidList = feedsList.getLongVideos().stream().map(longVideo -> longVideo.getVirtualSid()).distinct().toArray(String[]::new);
            //批量查询评论数据并且组装参数
            return longVideoCommentService.getCommentCountAsync(StringUtils.joinWith(",", virtualSidList)).thenCompose(ret -> {
                if (MapUtils.isNotEmpty(ret)) {
                    feedsList.getLongVideos().forEach(longVideo -> longVideo.setCmtCnt(ret.get(longVideo.getVirtualSid())));
                }
                rt.setRet(0);
                rt.setData(feedsList);

                return CompletableFuture.completedFuture(rt);
            });
        });
    }

    /**
     * 根据算法风行短视频返回，组装风行短视频详情
     *
     * @param reqProperty
     * @param response
     * @return
     */
    public CompletableFuture<VideoFeedsListRt> handleFxShort(FeedsListReqProperty reqProperty, LvtRecommendAlgorithmResponse response) {
        VideoFeedsListRt rt = new VideoFeedsListRt();
        if (response == null || CollectionUtils.isEmpty(response.getData())) {
            log.warn("empty lvtRecommendAlgorithmResponse, reqProperty:{}, response:{}", reqProperty, response);
            rt.setRet(1400);
            rt.setMsg("empty FeedsRecommendResponse");
            return CompletableFuture.completedFuture(rt);
        }
        List<LvtRecommendAlgorithmData> recommendDatas = response.getData();

        //加上入口视频的sid和vid信息，统一查询
        if (reqProperty.getRefreshTimes() == 0) {
            LvtRecommendAlgorithmData firstLongVideoData = new LvtRecommendAlgorithmData();
            firstLongVideoData.setId(reqProperty.getSid());
            firstLongVideoData.setVideoId(reqProperty.getVid());
            //放在第一个位置
            recommendDatas.add(0, firstLongVideoData);
        }

        Set<String> sidSet = new HashSet<>();
        Map<String, String> vidSidMap = new HashMap<>();
        //过滤算法返回结果中可能为空的sid/vid
        recommendDatas.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getId()) && StringUtils.isNotEmpty(data.getVideoId()))
                .forEach(data -> {
                    vidSidMap.put(data.getVideoId(), data.getId());
                    sidSet.add(data.getId());
                });

        //获取视频数据, vid-sid
        CompletableFuture<RpcResult<Map<String/*vid*/, UgcStandardVideo>>> videoMapFuture = ugcStandardVideoRpcApi.getByVidList(vidSidMap);

        return videoMapFuture.handleAsync((res, e) -> {

            RpcResult<Map<String, UgcStandardVideo>> videoMapResult = getFutureIgnoreException(videoMapFuture);
            Map<String, UgcStandardVideo> standardVideoMap = new HashMap<>();
            if (videoMapResult != null && videoMapResult.getCode() == 0 && videoMapResult.getData() != null) {
                standardVideoMap.putAll(videoMapResult.getData());
            }

            return handleUgcFeedsListResponse(standardVideoMap, recommendDatas, true);
        }).thenCompose(feedsList -> {
            if (feedsList == null) {
                rt.setRet(1400);
                rt.setMsg("feedList empty");

                return CompletableFuture.completedFuture(rt);
            }

            String[] virtualSidList = feedsList.getLongVideos().stream().map(longVideo -> longVideo.getVirtualSid()).distinct().toArray(String[]::new);
            //批量查询评论数据并且组装参数
            return longVideoCommentService.getCommentCountAsync(StringUtils.joinWith(",", virtualSidList)).thenCompose(ret -> {
                if (MapUtils.isNotEmpty(ret)) {
                    feedsList.getLongVideos().forEach(longVideo -> longVideo.setCmtCnt(ret.get(longVideo.getVirtualSid())));
                }
                rt.setRet(0);
                rt.setData(feedsList);

                return CompletableFuture.completedFuture(rt);
            });
        });
    }

    /**
     * 从算法接口取ugc数据
     *
     * @param standardVideoMap
     * @param recommendDatas
     * @return
     */
    private FeedsList handleUgcFeedsListResponse(Map<String, UgcStandardVideo> standardVideoMap,
                                                 List<LvtRecommendAlgorithmData> recommendDatas, boolean isFxShort) {

        FeedsList feedsList = new FeedsList();
        Map<String, StandardAlbum> sidMap = new HashMap<>();

        if (standardVideoMap.isEmpty()) {
            log.warn("handleFeedsListResponse, the standardAlbum or standardVideo of the recommend response is empty");
            return null;
        }
        if (isFxShort) {
            List<String> sidList = recommendDatas.stream()
                    .filter(e -> standardVideoMap.get(e.getVideoId()) != null)
                    .map(e -> standardVideoMap.get(e.getVideoId()).getLinkSid())
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());

            CompletableFuture<RpcResult<Map<String, StandardAlbum>>> sidMapFuture = CompletableFuture.completedFuture(null);
            if (CollectionUtils.isNotEmpty(sidList)) {
                sidMapFuture = albumRpcApi.getBySidsFilterInvalid(sidList);
            }
            RpcResult<Map<String, StandardAlbum>> sidMapResult = getFutureIgnoreException(sidMapFuture);
            if (sidMapResult != null && sidMapResult.getCode() == 0 && sidMapResult.getData() != null) {
                sidMap = sidMapResult.getData();
            }
        }

        for (LvtRecommendAlgorithmData data : recommendDatas) {
            UgcStandardVideo ugcStandardVideo = standardVideoMap.get(data.getVideoId());
            if (ugcStandardVideo == null) {
                continue;
            }

            LongVideo longVideo = new LongVideo();
            longVideo.setId("lv_" + ugcStandardVideo.getVid());
            longVideo.setTitle(ugcStandardVideo.getTitle());
            longVideo.setSid(ugcStandardVideo.getSid());
            longVideo.setHorizontalIcon(Collections.singletonList(ugcStandardVideo.getHorizontalIcon()));
            longVideo.setVerticalIcon(Collections.singletonList(ugcStandardVideo.getVerticalIcon()));
            longVideo.setVirtualSid(ugcStandardVideo.getVid());
            longVideo.setContentType("ugc");
//        longVideo.setAlgSource(longVideoTrailerRecommendConfig.getAlgSource());
            longVideo.setRecommendInfo(ugcStandardVideo.getAuthorName());
            longVideo.setCoverImage(Collections.singletonList(ugcStandardVideo.getHorizontalIcon()));
            longVideo.setAlbumFeatureType(10004);
            longVideo.setTags(ugcStandardVideo.getTag());
            longVideo.setAllowChaseAlbum(0);
            VideoPlayDetail videoPlayDetail = new VideoPlayDetail();
            videoPlayDetail.setSource(ugcStandardVideo.getSource());
            videoPlayDetail.setSourceAlbumId(ugcStandardVideo.getSourceAuthorId());
            videoPlayDetail.setSourceVideoId(ugcStandardVideo.getSourceVideoId());
            videoPlayDetail.setFeatureType(10004);
            videoPlayDetail.setVid(ugcStandardVideo.getVid());
            videoPlayDetail.setSourceVideoType(10004);
            videoPlayDetail.setDislikeReasons(getDisLikeReasons(null, null));
            longVideo.setVideoDetail(videoPlayDetail);
            feedsList.addLongVideo(longVideo);

            if (isFxShort) {
                longVideo.setSid(ugcStandardVideo.getLinkSid());
                longVideo.setContentType("fengxingShort");
                longVideo.setAlbumFeatureType(10086);
                if (StringUtils.isNotBlank(ugcStandardVideo.getLinkTitle())) {
                    longVideo.setTitle(ugcStandardVideo.getLinkTitle());
                }
                if (sidMap.get(ugcStandardVideo.getLinkSid()) != null) {
                    longVideo.setTags(sidMap.get(ugcStandardVideo.getLinkSid()).getTags());
                }
                videoPlayDetail.setTitle(ugcStandardVideo.getTitle());
                videoPlayDetail.setFeatureType(10086);
                videoPlayDetail.setSourceVideoType(10086);
                videoPlayDetail.setPlayUrl(ugcStandardVideo.getPlayUrl());
                String playUrl = ugcStandardVideo.getPlayUrl();
                if (StringUtils.isNotEmpty(playUrl)) {
                    String newPlayUrl = replaceAccessToken(playUrl);
                    videoPlayDetail.setPlayUrl(newPlayUrl);
                }
            }

            // 设置item
            Item item = new Item();
            item.setId(longVideo.getId());
            item.setMap(FeedsItemEnum.LONGVIDEO.getMap());
            feedsList.addItem(item);
        }

        return feedsList;
    }

    public String replaceAccessToken(String playUrl) {
        // 调用RPC接口获取accessToken
        CompletableFuture<String> accessTokenFuture = mediaCommonServiceRpcApi.fetchFunshionAccessToken().thenApply(response -> {
            if (response == null || StringUtils.isEmpty(response.getData())) {
                log.error("mediaCommonServiceRpcApi.fetchFunshionAccessToken() failed:");
                return null;
            }

            return response.getData();
        }).exceptionally(e -> {
            log.error("mediaCommonServiceRpcApi.fetchFunshionAccessToken() error:", e);
            return null;
        });

        String accessToken = FutureUtil.getFutureIgnoreException(accessTokenFuture);
        if (StringUtils.isEmpty(accessToken)) {
            log.error("fetch accessToken failed.");
            return playUrl;
        }

        // 定义匹配 access_token 参数的正则表达式
        String regex = "\\baccess_token=([^&]*)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(playUrl);

        if (matcher.find()) {
            // 如果找到，进行替换
            return playUrl.replaceFirst(matcher.group(0), "access_token=" + accessToken);
        } else {
            // 如果没找到，拼接在最后
            if (playUrl.contains("?")) {
                return playUrl + "&access_token=" + accessToken;
            } else {
                return playUrl + "?access_token=" + accessToken;
            }
        }
    }

    /**
     * 处理从算法接口获取的数据
     *
     * @param standardAlbumMap
     * @param standardVideoMap
     * @param vSidMap          可能为空
     * @param recommendDatas
     * @return
     */
    private FeedsList handleFeedsListResponse(Map<String, StandardAlbum> standardAlbumMap, Map<String, StandardVideo> standardVideoMap,
                                              Map<String, MisVirtualProgramRelation> vSidMap, List<LvtRecommendAlgorithmData> recommendDatas, Integer version) {
        FeedsList feedsList = new FeedsList();
        if (standardAlbumMap.isEmpty() || standardVideoMap.isEmpty()) {
            log.warn("handleFeedsListResponse, the standardAlbum or standardVideo of the recommend response is empty");
            return null;
        }

        for (LvtRecommendAlgorithmData data : recommendDatas) {
            StandardAlbum standardAlbum = standardAlbumMap.get(data.getId());
            StandardVideo standardVideo = standardVideoMap.get(data.getVideoId());
            if (standardAlbum == null || standardVideo == null) {
                continue;
            }

            if (StringUtils.isNotEmpty(standardAlbum.getSource()) &&
                    LongVideoConfig.ALBUM_SOURCE_YOUKU_MOBILE.equals(standardAlbum.getSource())) {
                continue;
            }

            //8.1.0版本以下 屏蔽风行和微迪欧
            if (StringUtils.isNotEmpty(standardAlbum.getSource()) && funshionLongVideoAndWeidiouFilterService.filterItemBySource(standardAlbum.getSource(), version)) {
                continue;
            }

            //6.8 以下版本 ，过滤掉  source=yst 的节目或视频
            if (StringUtils.isNotEmpty(standardAlbum.getSource()) &&
                    LongVideoConfig.ALBUM_SOURCE_YST.equals(standardAlbum.getSource()) && version < longVideoConfig.getYstFilterVersion()) {
                continue;
            }
            if (StringUtils.isNotEmpty(standardAlbum.getSource()) &&
                    LongVideoConfig.ALBUM_SOURCE_ZTV.equals(standardAlbum.getSource())) {
                continue;
            }

            LongVideo longVideo = buildLongVideoInfo(standardAlbum, standardVideo, vSidMap);
            feedsList.addLongVideo(longVideo);

            // 设置item
            Item item = new Item();
            item.setId(longVideo.getId());
            item.setMap(FeedsItemEnum.LONGVIDEO.getMap());
            feedsList.addItem(item);
        }

        return feedsList;
    }

    private LongVideo buildLongVideoInfo(StandardAlbum album, StandardVideo video, Map<String, MisVirtualProgramRelation> vSidMap) {
        LongVideo longVideo = new LongVideo();
        longVideo.setId("lv_" + video.getVid());
        longVideo.setTitle(album.getTitle());
        longVideo.setSid(album.getSid());
        longVideo.setHorizontalIcon(Collections.singletonList(album.getHorizontalIcon()));
        longVideo.setVerticalIcon(Collections.singletonList(album.getVerticalIcon()));
        String virtualSid = vSidMap.get(album.getSid()) != null ? vSidMap.get(album.getSid()).getVirtualSid() : album.getSid();
        longVideo.setVirtualSid(virtualSid);
        longVideo.setContentType(album.getProgramType());
        longVideo.setAlgSource(longVideoTrailerRecommendConfig.getAlgSource());
        longVideo.setRecommendInfo(album.getBrief());
        longVideo.setCoverImage(Collections.singletonList(video.getHorizontalIcon()));
        longVideo.setAlbumFeatureType(album.getFeatureType());

        if (!StringUtils.isEmpty(album.getTags())) {
            String tags = album.getTags().replace(" ", "").replace("|", "·");
            longVideo.setTags(tags);
        }
        VideoPlayDetail videoPlayDetail = new VideoPlayDetail();
        videoPlayDetail.setSource(album.getSource());
        videoPlayDetail.setSourceSite("sohu".equals(album.getSource()) ? video.getSourceSite() : null);
        videoPlayDetail.setSourceAlbumId(album.getSourceAlbumId());
        videoPlayDetail.setSourceVideoId(video.getSourceVideoId());
        videoPlayDetail.setFeatureType(video.getVideoType() == 0 ? 1 : 2);
        videoPlayDetail.setVid(video.getVid());
        videoPlayDetail.setTitle(video.getTitle());
        videoPlayDetail.setSourceVideoType(video.getVideoType());
        videoPlayDetail.setDislikeReasons(getDisLikeReasons(album.getTags(), album.getActor()));

        if (SourceEnum.YST.getDataSource().equalsIgnoreCase(video.getSource())
                && StringUtils.isNotBlank(video.getPlayUrl())) {
            List<YstPlayUrlBO> ystPlayUrlBOList = JsonUtil.getListFormStr(video.getPlayUrl(), YstPlayUrlBO.class);
            if (CollectionUtils.isNotEmpty(ystPlayUrlBOList)) {
                YstPlayUrlBO ystPlayUrlBO = ystPlayUrlBOList.stream().sorted(Comparator.comparing(YstPlayUrlBO::getDefinition))
                        .filter(p -> Objects.equals(DefinitionLevelEnum.HIGH.getLevel(), p.getDefinition()))
                        .findFirst().orElse(ystPlayUrlBOList.get(0));
                videoPlayDetail.setPlayUrl(ystPlayUrlBO.getVideoUrl());
            }
        }
        longVideo.setVideoDetail(videoPlayDetail);

        return longVideo;
    }

    /**
     * 获取负反馈
     *
     * @param tagStr
     * @param actorStr
     * @return
     */
    public List<String> getDisLikeReasons(String tagStr, String actorStr) {
        //设置节目剧头负反馈
        List<String> disLikeReasons = new ArrayList<>();
        disLikeReasons.addAll(FLOW_TRAILER_DISLIKE_REASON);

        List<String> programTypeList = LvProgramTypeUtils.getProgramTypeMap().values().stream().collect(Collectors.toList());

        List<String> otherReasons = new ArrayList<>();
        String[] tags = StringUtils.split(tagStr, "\\|");
        if (ArrayUtils.isNotEmpty(tags)) {
            for (String tag : tags) {
                if (otherReasons.size() < 3
                        && StringUtils.isNotBlank(tag)
                        && !programTypeList.contains(tag)
                        && !StringUtils.equals(tag, "其它")
                        && !StringUtils.equals(tag, "其他")) {
                    otherReasons.add("不想看：" + tag);
                }
            }
        }

        if (otherReasons.size() < 3) {
            String[] actors = StringUtils.split(actorStr, "\\|");
            if (ArrayUtils.isNotEmpty(actors)) {
                for (String actor : actors) {
                    if (otherReasons.size() < 3 && StringUtils.isNotBlank(actor) && !"未知".equals(actor) && !"无".equals(actor)) {
                        otherReasons.add("不想看：" + actor);
                    }
                }
            }
        }

        disLikeReasons.addAll(otherReasons);

        return disLikeReasons;
    }
}
