package com.heytap.video.youli.biz.contentmiddle;

import com.heytap.video.youli.config.YouliApiConfig;
import static com.heytap.video.youli.constant.GlobalConstant.APPID;
import com.heytap.video.youli.metric.YouliMetrics;
import com.heytap.video.youli.model.content.ResourceResult;
import com.heytap.video.youli.model.content.VideoResource;
import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.basic.heracles.client.core.spring.annotation.TextType;
import com.oppo.browser.common.app.lib.encryption.JsonTools;
import com.oppo.browser.common.app.lib.utils.SysInfo;
import com.oppo.browser.common.pubobj.feeds.runtimeexceptions.InternalServiceErrorRuntimeException;
import com.oppo.browser.common.pubobj.feeds.runtimeexceptions.ThirdPartyTimeoutRuntimeException;
import com.oppo.browser.common.pubobj.feeds.runtimeexceptions.ThirdPartyUnknownRuntimeException;
import com.oppo.browser.dfoob.channel.HttpDataChannel;
import com.oppo.browser.dfoob.channel.HttpDataChannelException;
import com.oppo.browser.video.common.pubobj.resource.FeedsListReqProperty;
import com.oppo.browser.video.common.pubobj.resource.GetPondVideosReqProperty;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MiddlePondResource {
    @Autowired
    private YouliApiConfig youliApiConfig;

    @Autowired
    private YouliMetrics youliMetrics;

    @Autowired
    private HttpDataChannel httpDataChannel;

    @HeraclesDynamicConfig(key = "pond.channel.pondId.json", fileName = "youli_api.properties", textType = TextType.JSON)
    public static Map<String, String> pondChannelPondIdMap = new HashMap<>();

    @HeraclesDynamicConfig(key = "pond.channel.tags.json", fileName = "youli_api.properties", textType = TextType.JSON)
    private Map<String, List<String>> pondChannelTagsMap = new HashMap<>();

    @HeraclesDynamicConfig(key = "pond.channel.pageSize", fileName = "youli_api.properties")
    private int pondChannelPageSize = 6;

    public CompletableFuture<List<VideoResource>> getPondVideos(GetPondVideosReqProperty reqProperty) {
        Map<String, Object> reqParams = getReqParams(reqProperty);
        CompletableFuture<ResourceResult> pondResource;
        try {
            pondResource = httpDataChannel.asyncPostForObject(
                    youliApiConfig.getVideoResourcePondUrl(), JsonTools.toJsonString(reqParams), ResourceResult.class, null, getCommonHeaders(), youliApiConfig.getVideoResourcePondTimeout());
        } catch (HttpDataChannelException e) {
            log.error("asyncPostForObject RequestPondVideos error, url:{},reqParams:{}", youliApiConfig.getVideoResourcePondUrl(), reqParams, e);
            throw new InternalServiceErrorRuntimeException("Http Data Channel Exception");
        }
        return pondResource.handle((resourceResult, throwable) -> {
            youliMetrics.pondApiRetReport(resourceResult);
            if (throwable != null || resourceResult == null) {
                log.warn("middle_pondResource_api: [url:{}][param:{}]", youliApiConfig.getVideoResourcePondUrl(), reqParams, throwable);
                throw new ThirdPartyTimeoutRuntimeException();
            }
            if (resourceResult.getCode() != 0 && resourceResult.getCode() != 4) {
                log.warn("getPondVideos the result is error,the req {} ,the code {}, the msg {}",
                        reqParams, resourceResult.getCode(), resourceResult.getMsg());
                throw new ThirdPartyUnknownRuntimeException(resourceResult.getCode() + ":" + resourceResult.getMsg());
            }
            if (resourceResult.getData() == null || CollectionUtils.isEmpty(resourceResult.getData().getResourceList())) {
                log.info("getPondVideos the result is empty,the req {} ,the code {}, the msg {}",
                        reqParams, resourceResult.getCode(), resourceResult.getMsg());
                return Collections.emptyList();
            }
            return resourceResult.getData().getResourceList();
        });
    }

    public CompletableFuture<List<VideoResource>> getPondChannelVideos(FeedsListReqProperty reqProperty) {
        GetPondVideosReqProperty pondReqProperty = new GetPondVideosReqProperty();
        pondReqProperty.setAttributeValues(reqProperty.getAttributeValues());
        pondReqProperty.setPondId(pondChannelPondIdMap.get(reqProperty.getFromId()));
        pondReqProperty.setPageSize(pondChannelPageSize);
        pondReqProperty.setTagName(pondChannelTagsMap.get(reqProperty.getFromId()));
        //不区上下刷，第一刷开始作为起始
        if(reqProperty.getRefreshTimes() == null || reqProperty.getRefreshTimes() == 0){
            pondReqProperty.setPageNum(1);
            int stamp = reqProperty.getFirstRefreshTime() == 0 ? SysInfo.getUnixTimeStamp() : (int) (reqProperty.getFirstRefreshTime() / 1000);
            pondReqProperty.setMaxTimestamp(stamp);
        } else {
            //非第一刷
            pondReqProperty.setPageNum(reqProperty.getRefreshTimes() + 1);
            Integer bottomTime = reqProperty.getBottomtime();
            if (reqProperty.getFirstRefreshTime() == 0) {
                int stamp = (bottomTime == null || bottomTime == 0) ? SysInfo.getUnixTimeStamp() : bottomTime;
                pondReqProperty.setMaxTimestamp(stamp);
            } else {
                pondReqProperty.setMaxTimestamp((int) (reqProperty.getFirstRefreshTime() / 1000));
            }
        }
        reqProperty.setBottomtime(pondReqProperty.getMaxTimestamp());
        return getPondVideos(pondReqProperty);
    }

    private Map<String, Object> getReqParams(GetPondVideosReqProperty reqProperty) {
        Map<String, Object> reqParams = new HashMap<>();
        reqParams.put(APPID, 2);
        reqParams.put("pondId", reqProperty.getPondId());
        reqParams.put("page", reqProperty.getPageNum());
        reqParams.put("pageSize", reqProperty.getPageSize());
        reqParams.put("contentType", 2);
        if(reqProperty.getMaxTimestamp() != 0){
            reqParams.put("maxTimestamp", reqProperty.getMaxTimestamp());
        }
        if(CollectionUtils.isNotEmpty(reqProperty.getTagName())){
            reqParams.put("tagName", reqProperty.getTagName());
        }
        if(reqProperty.getAttributeValues() != null && reqProperty.getAttributeValues().getBuuid() != 0){
            reqParams.put("buuid", String.valueOf(reqProperty.getAttributeValues().getBuuid()));
        }
        if(StringUtils.isNotEmpty(reqProperty.getTopCategory())){
            reqParams.put("topCategory", reqProperty.getTopCategory());
        }
        reqParams.put("needRelation", Boolean.TRUE);
        return reqParams;
    }

    private Map<String, String> getCommonHeaders() {
        Map<String, String> headers = new HashMap<>(2);
        headers.put(APPID, "2");
        headers.put("Content-Type", "application/json");
        return headers;
    }
}
