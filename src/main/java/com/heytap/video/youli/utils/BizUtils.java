package com.heytap.video.youli.utils;

import com.heytap.video.youli.config.YouliApiConfig;
import com.heytap.video.youli.constant.Bidlst;

/**
 * <AUTHOR>
 * @Description
 * @data 2025/1/3 17:47
 */
public class BizUtils {

    public static final String RELEVANT_BIDLST_KEY = "relevant";

    /**
     * 获取相关推荐的bidLst
     * @param youliApiConfig
     * @return
     */
    public static String getRelevantBidLst(YouliApiConfig youliApiConfig) {
        return youliApiConfig.getRecBidLstMap().getOrDefault(RELEVANT_BIDLST_KEY, Bidlst.RELEVANT);
    }
}
