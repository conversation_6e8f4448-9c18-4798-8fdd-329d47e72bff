package com.heytap.video.youli.utils;

import com.oppo.browser.app.framework.threadlocal.StringBuilderHolder;
import com.oppo.browser.common.app.lib.utils.MD5Util;

import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

public class SignUtil {

    /**
     * 对中台更新统计数据的请求构造sign
     */
    public static String makeSignForResourceApi(TreeMap<String, Object> map) {
        StringBuilder stringBuilder = StringBuilderHolder.getStringBuilder();
        //对map进行排序
        Set<Map.Entry<String, Object>> entrySet = map.entrySet();
        //进行循环处理
        for (Map.Entry<String, Object> entry : entrySet) {
            if ("timestamp".equals(entry.getKey())) {
                stringBuilder.append(entry.getValue()).append("&");
            } else {
                stringBuilder.append(entry.getKey()).append("&");
            }
        }
        //具体source
        String source = stringBuilder.substring(0, stringBuilder.length() - 1);
        return MD5Util.toMD5(source);
    }

}
