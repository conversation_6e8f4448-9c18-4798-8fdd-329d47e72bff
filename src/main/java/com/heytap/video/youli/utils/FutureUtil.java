package com.heytap.video.youli.utils;

import java.util.concurrent.Future;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2022/10/14 14:21
 */

public class FutureUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(FutureUtil.class);

    public static <T> T getFutureIgnoreException(Future<T> future) {
        try {
            return (future != null) ? future.get() : null;
        } catch (Exception e) {
            LOGGER.error("error get result from future", e);
            return null;
        }
    }
}