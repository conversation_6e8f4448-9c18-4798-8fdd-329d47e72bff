package com.heytap.video.youli.utils;

import com.oppo.browser.app.framework.utils.AppConstant;

import java.util.HashMap;
import java.util.Map;

public class UrlUtil {

    public static Map<String, String> getContentCommonHeaders() {
        Map<String, String> headers = new HashMap<>(3);
        headers.put("appId", "2");
        headers.put(AppConstant.CONTENT_TYPE, AppConstant.CONTENT_TYPE_JSON_CHARSET_UTF8);
        return headers;
    }
}
