package com.heytap.video.youli.utils;

import java.util.HashMap;

/**
 * @Author: 80339123 liu ying
 * @Date: 2022/2/22 19:11
 */
public class LvProgramTypeUtils {

    private static final HashMap<String, String> PROGRAM_TYPE_MAP = new HashMap<String, String>() {
        {
            put("tv", "电视剧");
            put("movie", "电影");
            put("doc", "纪录片");
            put("comic", "动漫");
            put("show", "综艺");
            put("kids", "少儿");
            put("eSports", "电竞");
            put("live", "直播");
            put("music", "音乐");
            put("game", "游戏");
            put("ent", "娱乐");
            put("life", "生活");
            put("edu", "教育");
            put("origin", "原创");
            put("sports", "体育");
        }
    };

    public static String getName(String programType) {
        return PROGRAM_TYPE_MAP.get(programType);
    }

    public static HashMap<String, String> getProgramTypeMap() {
        return PROGRAM_TYPE_MAP;
    }
}
