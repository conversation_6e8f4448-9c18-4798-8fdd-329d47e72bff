//package com.heytap.video.youli.config;
//
//import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesConfigUpdateListener;
//import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
//import com.oppo.browser.app.framework.utils.Constant;
//import org.springframework.context.annotation.Configuration;
//
///**
// * <AUTHOR>
// */
//@Configuration
//public class FeedsAccessConfig {
//    /**
//     * 视频播放地址302功能跳转的URL域名
//     */
//    @HeraclesDynamicConfig(key = "feeds_access", fileName = "configure.properties")
//    private static String feedsAccess = "https://i.youlishipin.com";
//
//    /**
//     * 视频播放地址阿里云OSS的访问路径域名
//     */
//    @HeraclesDynamicConfig(key = "oss_access", fileName = "configure.properties")
//    private static String ossAccess = "https://youlishipin-com.oss-cn-beijing.aliyuncs.com,http://youlishipin-com.oss-cn-beijing.aliyuncs.com";
//
//    private static String[] ossAccessList = ossAccess.split(Constant.COMMA);
//
//    /**
//     * 图片访问地址阿里云OSS的访问路径域名
//     */
//    @HeraclesDynamicConfig(key = "oss_access_img", fileName = "configure.properties")
//    private static String ossAccessImg = "https://img-cdn-youlishipin-com.oss-cn-beijing.aliyuncs.com,http://img-cdn-youlishipin-com.oss-cn-beijing.aliyuncs.com";
//
//    private static String[] ossAccessImgList = ossAccessImg.split(Constant.COMMA);
//
//    /**
//     * 图片访问地址cdn域名
//     */
//    @HeraclesDynamicConfig(key = "cdn_access_img", fileName = "configure.properties")
//    private static String cdnAccessImg = "https://img-cdn.youlishipin.com";
//
//    public static String getFeedsAccess() {
//        return FeedsAccessConfig.feedsAccess;
//    }
//
//    public static void setFeedsAccess(String feedsAccess) {
//        FeedsAccessConfig.feedsAccess = feedsAccess;
//    }
//
//    public static String getOssAccess() {
//        return ossAccess;
//    }
//
//    @HeraclesConfigUpdateListener(key = "oss_access", fileName = "configure.properties")
//    public static void setOssAccess(String k, String newValue, String oldValue) {
//        FeedsAccessConfig.ossAccess = newValue;
//        FeedsAccessConfig.ossAccessList = newValue.split(Constant.COMMA);
//    }
//
//    public static String[] getOssAccessList() {
//        return ossAccessList;
//    }
//
//    public static String getOssAccessImg() {
//        return ossAccessImg;
//    }
//
//    @HeraclesConfigUpdateListener(key = "oss_access_img", fileName = "configure.properties")
//    public static void setOssAccessImg(String k, String newValue, String oldValue) {
//        FeedsAccessConfig.ossAccessImg = newValue;
//        FeedsAccessConfig.ossAccessImgList = newValue.split(Constant.COMMA);
//    }
//
//    public static String[] getOssAccessImgList() {
//        return ossAccessImgList;
//    }
//
//    public static String getCdnAccessImg() {
//        return cdnAccessImg;
//    }
//
//    public static void setCdnAccessImg(String cdnAccessImg) {
//        FeedsAccessConfig.cdnAccessImg = cdnAccessImg;
//    }
//}