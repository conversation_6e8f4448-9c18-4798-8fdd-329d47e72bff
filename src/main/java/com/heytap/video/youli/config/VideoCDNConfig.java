//package com.heytap.video.youli.config;
//
//import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
//import org.springframework.context.annotation.Configuration;
//
//@Configuration
//public class VideoCDNConfig {
//    @HeraclesDynamicConfig(key = "cdn.privateKey", fileName = "cdn.properties")
//    private static String privateKey = "85a2599f24e6936b34de22bc645ad9eb";
//
//    @HeraclesDynamicConfig(key = "cdn.video.url", fileName = "cdn.properties")
//    private static String videoUrl = "https://video-cdn.youlishipin.com";
//
//    public static String getPrivateKey() {
//        return privateKey;
//    }
//
//    public static void setPrivateKey(String privateKey) {
//        VideoCDNConfig.privateKey = privateKey;
//    }
//
//    public static String getVideoUrl() {
//        return videoUrl;
//    }
//
//    public static void setVideoUrl(String videoUrl) {
//        VideoCDNConfig.videoUrl = videoUrl;
//    }
//}