package com.heytap.video.youli.config;

import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.basic.heracles.client.core.spring.annotation.TextType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
@Slf4j
public class YouliApiConfig {

    @HeraclesDynamicConfig(key = "youli.recommend.url", fileName = "youli_api.properties")
    private String mainRecommendUrl; //主推荐频道url

    @HeraclesDynamicConfig(key = "youli.channel.rec.url", fileName = "youli_api.properties")
    private String channelRecommendUrl; //非主推荐频道url

    @HeraclesDynamicConfig(key = "youli.recommend.timeOut", fileName = "youli_api.properties")
    private Integer recommendSocketTimeout = 1024;

    @HeraclesDynamicConfig(key = "youli.recommend.eachRefreshLimit", fileName = "youli_api.properties")
    private Integer eachRefreshNum = 8;

    @HeraclesDynamicConfig(key = "youli.recommend.gameNum", fileName = "youli_api.properties")
    private Integer gameNum = 4;

    @HeraclesDynamicConfig(key = "youli.relevant.url", fileName = "youli_api.properties")
    private String detailRelevantUrl; //详情页相关推荐url

    @HeraclesDynamicConfig(key = "youli.relevant.timeOut", fileName = "youli_api.properties")
    private Integer relevantSocketTimeout = 1024;

    @HeraclesDynamicConfig(key = "youli.relevant.num", fileName = "youli_api.properties")
    private Integer videoRecLimit = 20;

    /**
     * detailStyle=3 2N详情页 仅获取4个相关推荐
     */
    @HeraclesDynamicConfig(key = "youli.relevant.2N.num", fileName = "youli_api.properties")
    private Integer videoRec2NLimit = 4;

    @HeraclesDynamicConfig(key = "video.resource.pond.url", fileName = "youli_api.properties")
    private String videoResourcePondUrl;//内容池url

    @HeraclesDynamicConfig(key = "video.resource.pond.timeout", fileName = "youli_api.properties")
    private int videoResourcePondTimeout = 512;

    @HeraclesDynamicConfig(key = "video.resource.url", fileName = "youli_api.properties")
    private String videoResourceUrl = "";

    @HeraclesDynamicConfig(key = "video.resource.timeout", fileName = "youli_api.properties")
    private Integer videoResourceTimeOut = 512;

    @HeraclesDynamicConfig(key = "author.profile.url", fileName = "youli_api.properties")
    private String authorProfileUrl = "";

    @HeraclesDynamicConfig(key = "author.profile.timeout", fileName = "youli_api.properties")
    private Integer authorProfileTimeOut = 512;

    @HeraclesDynamicConfig(key = "author.batch.profile.url", fileName = "youli_api.properties")
    private String authorBatchProfileUrl = "";

    @HeraclesDynamicConfig(key = "author.batch.profile.timeout", fileName = "youli_api.properties")
    private Integer authorBatchProfileTimeOut = 512;

    @HeraclesDynamicConfig(key = "author.videos.url", fileName = "youli_api.properties")
    private String authorVideosUrl = "";

    @HeraclesDynamicConfig(key = "author.videos.timeout", fileName = "youli_api.properties")
    private Integer authorVideosTimeOut = 512;

    @HeraclesDynamicConfig(key = "all.search.long.num", fileName = "youli_api.properties")
    private Integer allSearchLongNum = 3;


    @HeraclesDynamicConfig(key = "rec.channelId.json", fileName = "youli_api.properties", textType = TextType.JSON)
    public Map<String, String> recChannelIdMap = new HashMap<>();

    @HeraclesDynamicConfig(key = "rec.bidLst.json", fileName = "youli_api.properties", textType = TextType.JSON)
    public Map<String, String> recBidLstMap = new HashMap<>();

    public String getMainRecommendUrl() {
        return mainRecommendUrl;
    }

    public String getChannelRecommendUrl() {
        return channelRecommendUrl;
    }

    public Map<String, String> getRecChannelIdMap() {
        return recChannelIdMap;
    }

    public Map<String, String> getRecBidLstMap() {
        return recBidLstMap;
    }

    public String getDetailRelevantUrl() {
        return detailRelevantUrl;
    }

    public Integer getRecommendSocketTimeout() {
        return recommendSocketTimeout;
    }

    public Integer getRelevantSocketTimeout() {
        return relevantSocketTimeout;
    }

    public Integer getEachRefreshNum() {
        return eachRefreshNum;
    }

    public Integer getVideoRecLimit() {
        return videoRecLimit;
    }

    public String getVideoResourcePondUrl() {
        return videoResourcePondUrl;
    }

    public int getVideoResourcePondTimeout() {
        return videoResourcePondTimeout;
    }

    public String getVideoResourceUrl() {
        return videoResourceUrl;
    }

    public Integer getVideoResourceTimeOut() {
        return videoResourceTimeOut;
    }

    public String getAuthorProfileUrl() {
        return authorProfileUrl;
    }

    public Integer getAuthorProfileTimeOut() {
        return authorProfileTimeOut;
    }

    public String getAuthorBatchProfileUrl() {
        return authorBatchProfileUrl;
    }

    public Integer getAuthorBatchProfileTimeOut() {
        return authorBatchProfileTimeOut;
    }

    public String getAuthorVideosUrl() {
        return authorVideosUrl;
    }

    public Integer getAuthorVideosTimeOut() {
        return authorVideosTimeOut;
    }

    public Integer getGameNum() {
        return gameNum;
    }

    public Integer getAllSearchLongNum() {
        return allSearchLongNum;
    }

    public Integer getVideoRec2NLimit() {
        return videoRec2NLimit;
    }
}
