package com.heytap.video.youli.config;

import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import com.oppo.basic.heracles.client.core.spring.annotation.TextType;
import lombok.Getter;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/12 16:03
 */
@Configuration
@Getter
public class LongVideoTrailerRecommendConfig {
    @HeraclesDynamicConfig(key = "recommend.algorithm.route", fileName = "lvt_recommend_config.properties")
    private String recommendAlgorithmRoute = "bjlongvideodev";

    @HeraclesDynamicConfig(key = "recommend.algorithm.cid", fileName = "lvt_recommend_config.properties")
    private String recommendAlgorithmCid = "feeds";

    @HeraclesDynamicConfig(key = "recommend.algorithm.fromId", fileName = "lvt_recommend_config.properties")
    private String recommendAlgorithmFromId;

    @HeraclesDynamicConfig(key = "recommend.algorithm.channelId", fileName = "lvt_recommend_config.properties")
    private String recommendAlgorithmChannelId = "6IqS5p6c";

    @HeraclesDynamicConfig(key = "recommend.algorithm.url", fileName = "lvt_recommend_config.properties")
    private String recommendAlgorithmUrl;

    @HeraclesDynamicConfig(key = "recommend.algorithm.timeout", fileName = "lvt_recommend_config.properties")
    private Integer recommendAlgorithmTimeout = 1000;

    @HeraclesDynamicConfig(key = "recommend.algorithm.bidListMap", fileName = "lvt_recommend_config.properties", textType = TextType.JSON)
    private Map<Integer, String> bidList;

    @HeraclesDynamicConfig(key = "recommend.algorithm.videoRecLimit", fileName = "lvt_recommend_config.properties")
    private Integer videoRecLimit = 6;

    @HeraclesDynamicConfig(key = "recommend.algorithm.algSource", fileName = "lvt_recommend_config.properties")
    private String algSource;

    @HeraclesDynamicConfig(key = "ugc.algorithm.url", fileName = "lvt_recommend_config.properties")
    private String ugcAlgorithmUrl;

    @HeraclesDynamicConfig(key = "default.ugc.pool", fileName = "lvt_recommend_config.properties")
    private String defaultUgcPool;

    @HeraclesDynamicConfig(key = "default.fxs.pool", fileName = "lvt_recommend_config.properties")
    private String defaultFxsPool;
}