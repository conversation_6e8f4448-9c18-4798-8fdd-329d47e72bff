package com.heytap.video.youli.config;

import com.heytap.video.youli.spi.common.YouliVideoCommentCountSPI;
import com.oppo.cpc.video.framework.lib.thirdparty.spi.PostHandleSPIRegister;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static com.heytap.video.thirdparty.constant.SPIServiceType.SEARCH;


/**
 * 后置处理SPI配置,应用场景是：可以配置多个服务都需要使用的SPI，比如获取评论数，视频搜索，视频详情接口都需要使用
 *
 * <AUTHOR>
 */
@Configuration
public class PostHandleSPIRegisterConfiguration {

    @Bean
    public PostHandleSPIRegister postHandleSPIRegister(YouliVideoCommentCountSPI youliVideoCommentCountSPI){
        return new PostHandleSPIRegister().register(youliVideoCommentCountSPI, SEARCH.getCode());
    }
}
