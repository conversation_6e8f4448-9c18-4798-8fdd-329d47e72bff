package com.heytap.video.youli.config;

import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import java.security.SecureRandom;
import javax.annotation.PostConstruct;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ResourceConfig {

    @HeraclesDynamicConfig(key = "video_detail_url", fileName = "videoResourceConf.properties")
    private static String videoDetailUrl;

    @HeraclesDynamicConfig(key = "video_comment_url", fileName = "videoResourceConf.properties")
    private static String videoCommentUrl;

    @HeraclesDynamicConfig(key = "selfvideo_medianame", fileName = "videoResourceConf.properties")
    private static String mediaName;

    @HeraclesDynamicConfig(key = "default_author_avatar", fileName = "videoResourceConf.properties")
    private static String defaultAuthorAvatar;

    @HeraclesDynamicConfig(key = "default_video_summary", fileName = "videoResourceConf.properties")
    private static String defaultSummary;

    @HeraclesDynamicConfig(key = "cmt.append.url", fileName = "videoResourceConf.properties")
    private static String cmtAppendUrl = "";

    @HeraclesDynamicConfig(key = "cmt.show.num", fileName = "videoResourceConf.properties")
    private static boolean cmtShowNum = true;

    @HeraclesDynamicConfig(key = "cmt.type", fileName = "videoResourceConf.properties")
    private static Integer cmtType;

    private static String[] mediaNames;

    @HeraclesDynamicConfig(key = "minPlaycnt", fileName = "videoResourceConf.properties")
    private static Integer minPlaycnt;

    @HeraclesDynamicConfig(key = "maxPlaycnt", fileName = "videoResourceConf.properties")
    private static Integer maxPlaycnt;

    @HeraclesDynamicConfig(key = "minLikecnt", fileName = "videoResourceConf.properties")
    private static Integer minLikecnt;

    @HeraclesDynamicConfig(key = "maxLikecnt", fileName = "videoResourceConf.properties")
    private static Integer maxLikecnt;

    @HeraclesDynamicConfig(key = "recIssuedReason", fileName = "videoResourceConf.properties")
    private static String recIssuedReason;

    @HeraclesDynamicConfig(key = "deepLinkUrl", fileName = "videoResourceConf.properties")
    private static String deepLinkUrl;

    @PostConstruct
    private static void initMediaName() {
        setMediaNames(mediaName.split(";"));
    }

    public static String getDefaultSummary() {
        return defaultSummary;
    }

    public static void setDefaultSummary(String defaultSummary) {
        ResourceConfig.defaultSummary = defaultSummary;
    }

    public static String getDefaultAuthorAvatar() {
        return defaultAuthorAvatar;
    }

    public static void setDefaultAuthorAvatar(String defaultAuthorAvatar) {
        ResourceConfig.defaultAuthorAvatar = defaultAuthorAvatar;
    }

    public static void setVideoDetailUrl(String videoDetailUrl) {
        ResourceConfig.videoDetailUrl = videoDetailUrl;
    }

    public static String getVideoDetailUrl() {
        return videoDetailUrl;
    }

    public static void setVideoCommentUrl(String videoCommentUrl) {
        ResourceConfig.videoCommentUrl = videoCommentUrl;
    }

    public static String getVideoCommentUrl() {
        return videoCommentUrl;
    }

    public static String getMediaName() {
        return mediaName;
    }

    public static void setMediaName(String mediaName) {
        ResourceConfig.mediaName = mediaName;
    }

    public static String[] getMediaNames() {
        return mediaNames;
    }

    public static void setMediaNames(String[] mediaNames) {
        ResourceConfig.mediaNames = mediaNames;
    }

    public static String getMediaName(String docId) {
        if (docId == null) {
            return mediaNames[new SecureRandom().nextInt(mediaNames.length)];
        }
        return mediaNames[Math.abs(docId.hashCode() % mediaNames.length)];
    }

    public static Integer getMinPlaycnt() {
        return minPlaycnt;
    }

    public static void setMinPlaycnt(Integer minPlaycnt) {
        ResourceConfig.minPlaycnt = minPlaycnt;
    }

    public static Integer getMaxPlaycnt() {
        return maxPlaycnt;
    }

    public static void setMaxPlaycnt(Integer maxPlaycnt) {
        ResourceConfig.maxPlaycnt = maxPlaycnt;
    }

    public static Integer getMinLikecnt() {
        return minLikecnt;
    }

    public static void setMinLikecnt(Integer minLikecnt) {
        ResourceConfig.minLikecnt = minLikecnt;
    }

    public static Integer getMaxLikecnt() {
        return maxLikecnt;
    }

    public static void setMaxLikecnt(Integer maxLikecnt) {
        ResourceConfig.maxLikecnt = maxLikecnt;
    }

    public static String getRecIssuedReason() {
        return recIssuedReason;
    }

    public static void setRecIssuedReason(String recIssuedReason) {
        ResourceConfig.recIssuedReason = recIssuedReason;
    }

    public static String getDeepLinkUrl() {
        return deepLinkUrl;
    }

    public static void setDeepLinkUrl(String deeplinkUrl) {
        ResourceConfig.deepLinkUrl = deeplinkUrl;
    }

    public static String getCmtAppendUrl() {
        return cmtAppendUrl;
    }

    public static void setCmtAppendUrl(String cmtAppendUrl) {
        ResourceConfig.cmtAppendUrl = cmtAppendUrl;
    }

    public static boolean isCmtShowNum() {
        return cmtShowNum;
    }

    public static void setCmtShowNum(boolean cmtShowNum) {
        ResourceConfig.cmtShowNum = cmtShowNum;
    }

    public static Integer getCmtType() {
        return cmtType;
    }

    public static void setCmtType(Integer cmtType) {
        ResourceConfig.cmtType = cmtType;
    }
}