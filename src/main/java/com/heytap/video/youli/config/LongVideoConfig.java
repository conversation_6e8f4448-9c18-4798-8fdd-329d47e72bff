package com.heytap.video.youli.config;

import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import lombok.Getter;
import org.springframework.stereotype.Component;

/*
 * 长视频评论、用户关系（追剧|预约）相关配置
 */
@Component
@Getter
public class LongVideoConfig {
    @HeraclesDynamicConfig(key = "commentCount.url", fileName = "longVideo.properties")
    private String commentCountUrl;

    @HeraclesDynamicConfig(key = "commentCount.timeout", fileName = "longVideo.properties")
    private int commentCountTimeout;

    @HeraclesDynamicConfig(key = "comment.businessType", fileName = "longVideo.properties")
    private String commentBusinessType = "long_video";

    //边视频推荐流	 B1655276899003	 场景
    public Integer algorithmTypeOne = 1;

    //内容池周边视频推荐 B1655276899006 场景
    public Integer algorithmTypeTwo = 2;

    //内容池UGC视频推荐 XXXXXX 场景
    public Integer algorithmTypeUGC = 3;

    //内容池风行短视频推荐 XXXXXX 场景
    public Integer algorithmTypeFXS = 4;

    //悦视通
    public static final String ALBUM_SOURCE_YST = "yst";

    //Z视介
    public static final String ALBUM_SOURCE_ZTV = "ztv";

    //优酷移动端
    public static final String ALBUM_SOURCE_YOUKU_MOBILE = "youkumobile";

    @HeraclesDynamicConfig(key = "yst.filter.version", fileName = "longVideo.properties")
    private Integer ystFilterVersion;
}
