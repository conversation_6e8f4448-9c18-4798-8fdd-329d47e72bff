package com.heytap.video.youli.config;

import com.oppo.basic.heracles.client.core.spring.annotation.HeraclesDynamicConfig;
import lombok.Data;
import org.springframework.stereotype.Component;

/*
 * Description 用户相关配置
 * Date 17:45 2022/1/17
 * Author songjiajia 80350688
*/
@Component
@Data
public class ThreadPoolProperties {

    @HeraclesDynamicConfig(key = "common.corePoolSize", fileName = "threadpool_config.properties")
    private int commonCorePoolSize;

    @HeraclesDynamicConfig(key = "common.maxPoolSize", fileName = "threadpool_config.properties")
    private int commonMaxPoolSize;

    @HeraclesDynamicConfig(key = "common.queueCapacity", fileName = "threadpool_config.properties")
    private int commonQueueCapacity;
}
