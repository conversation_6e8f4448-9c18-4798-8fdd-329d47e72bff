package com.heytap.video.youli.metric;

import com.oppo.browser.video.common.pubobj.resource.FeedsListReqProperty;
import com.oppo.browser.video.common.pubobj.resultObj.feedsList.FeedsList;
import com.oppo.browser.video.common.pubobj.resultObj.news.VideoFeedsListRt;
import com.oppo.browser.video.common.utils.UrlUtils;
import com.oppo.cpc.video.framework.lib.metrics.MonitorUtil;
import com.oppo.cpc.video.framework.lib.metrics.TagConstants;
import com.heytap.video.youli.config.YouliApiConfig;
import com.heytap.video.youli.model.algorithm.RecommendList;
import com.heytap.video.youli.model.content.ResourceResult;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Component
public class YouliMetrics {

    @Autowired
    YouliApiConfig youliApiConfig;

    private static List<Integer> feedsListRangeCountList = Arrays.asList(new Integer[]{0, 3, 7, 10, Integer.MAX_VALUE});

    public void listCount(VideoFeedsListRt response, FeedsListReqProperty reqProperty) {
        if (response == null) {
            return;
        }

        String fromId = reqProperty.getFromId();
        FeedsList feedsList = response.getData();
        Boolean fallback = feedsList == null ? false : feedsList.getFallback();
        int newsCount = feedsList == null ? 0 : CollectionUtils.size(feedsList.getItems());

        MonitorUtil.counter(MetricConstant.METERICS_VIDEO_YOULI_GET_LIST, newsCount,
                TagConstants.CHANNEL, fromId, MetricConstant.TAG_FALLBACK, fallback);

        String template = "_%s_%s";
        String suffix = String.format(template, feedsListRangeCountList.get(feedsListRangeCountList.size() - 2),
                feedsListRangeCountList.get(feedsListRangeCountList.size() - 1));
        for (int i = 0; i < feedsListRangeCountList.size() - 2; i++) {
            if ((newsCount >= feedsListRangeCountList.get(i)) && (newsCount < feedsListRangeCountList.get(i + 1))) {
                suffix = String.format(template, feedsListRangeCountList.get(i), feedsListRangeCountList.get(i + 1));
                break;
            }
        }

        MonitorUtil.counter(MetricConstant.METERICS_VIDEO_YOULI_GET_LIST_RANGE_COUNT + suffix, newsCount,
                TagConstants.CHANNEL, fromId, MetricConstant.TAG_FALLBACK, fallback);
        MonitorUtil.counter(MetricConstant.METERICS_VIDEO_YOULI_GET_LIST_RANGE_TIMES + suffix, 1,
                TagConstants.CHANNEL, fromId, MetricConstant.TAG_FALLBACK, fallback);
    }


    /**
     * 内容池接口返回码上报
     *
     * @param response
     */
    public void pondApiRetReport(ResourceResult response) {
        String path = UrlUtils.getUrlPath(youliApiConfig.getVideoResourcePondUrl());

        MonitorUtil.counter(MetricConstant.METERICS_VIDEO_YOULI_API_RET, 1, MetricConstant.TAG_API,
                path, MetricConstant.TAG_RET, response == null ? -1 : response.getCode());
    }

    /**
     * 推荐接口返回码上报
     *
     * @param recommendList
     * @param reqProperty
     */
    public void recommendApiRetReport(RecommendList recommendList, FeedsListReqProperty reqProperty) {
        String url = "video".equals(reqProperty.getFromId()) ? youliApiConfig.getMainRecommendUrl() : youliApiConfig.getChannelRecommendUrl();
        String path = UrlUtils.getUrlPath(url);

        int ret = recommendList == null ? -1 : (int)recommendList.getCode();

        MonitorUtil.counter(MetricConstant.METERICS_VIDEO_YOULI_API_RET, 1, MetricConstant.TAG_API,
                path, MetricConstant.TAG_RET, ret);
    }


    /**
     * 推荐算法降级次数
     *
     * @param feedsList
     * @param reqProperty
     */
    public void recommendFallbackReport(FeedsList feedsList, FeedsListReqProperty reqProperty) {
        String interfaceName = "video".equals(reqProperty.getFromId()) ? "mainRec" : "channelRec";

        MonitorUtil.counter(MetricConstant.METERICS_VIDEO_YOULI_FALLBACK, 1,
                MetricConstant.TAG_API, interfaceName, MetricConstant.TAG_RESULT, feedsList == null ? MetricConstant.RESULT_ERR : MetricConstant.RESULT_SUCC);

    }

    /**
     * 详情接口返回码上报
     *
     * @param batchResource
     */
    public void resourceApiRetReport(ResourceResult batchResource) {
        String path = UrlUtils.getUrlPath(youliApiConfig.getVideoResourceUrl());
        int ret = batchResource == null ? -1 : batchResource.getCode();

        MonitorUtil.counter(MetricConstant.METERICS_VIDEO_YOULI_API_RET, 1, MetricConstant.TAG_API,
                path, MetricConstant.TAG_RET, ret);
    }

    /**
     * 详情降级次数
     *
     * @param feedsList
     */
    public void resourceFallbackReport(FeedsList feedsList) {

        MonitorUtil.counter(MetricConstant.METERICS_VIDEO_YOULI_FALLBACK, 1,
                MetricConstant.TAG_API, "midResource", MetricConstant.TAG_RESULT, feedsList == null ? MetricConstant.RESULT_ERR : MetricConstant.RESULT_SUCC);

    }

}
