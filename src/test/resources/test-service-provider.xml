<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
http://code.alibabatech.com/schema/dubbo
http://code.alibabatech.com/schema/dubbo/dubbo.xsd">
    <!-- 消费方应用名，用于计算依赖关系，不是匹配条件，不要与提供方一样 -->
    <dubbo:application name="video-youli-service">
        <dubbo:parameter key="qos.enable" value="false" />
    </dubbo:application>

    <!-- 使用本地伪集群注册中心暴露发现服务地址 -->
    <dubbo:registry address="multicast://*********:1234" check="false" />

    <dubbo:protocol id="dubbo" name="dubbo" threads="${dubbo.threads}" port="#{systemEnvironment['paas_port_1']}">
    </dubbo:protocol>

    <!--全局标记，推荐使用-->
    <dubbo:provider>
        <!-- 服务分类标记 -->
        <dubbo:parameter key="provider.category" value="${paas_instance_tag:}"/>

        <!-- 路由模式
       route.mode路由模式，行为主要分为以下三类：
       exception：默认，抛出no provider异常；
       common: 表示路由至默认标签节点（无标签节点）；
       all: 表示路由至所有后端节点。
       route.mode参数可在服务端配置，但必须所有提供者配置相同值，若服务端不一致，请在客户端指定最终需要使用的mode值。
       两端至少有一端指定该参数，若均未指定，则程序启动失败 -->
        <dubbo:parameter key="route.mode" value="all"/>
    </dubbo:provider>

    <dubbo:service interface="com.oppo.browser.video.common.service.video.IVideoSourceAdapter" ref="iVideoSourceAdapter"
                   protocol="dubbo" group="youli" dynamic="true" />
    <bean id="iVideoSourceAdapter" class="com.heytap.video.youli.service.impl.IVideoYouliAdapterImpl" />

    <dubbo:service interface="com.oppo.browser.video.common.service.video.IVideoYouli" ref="iVideoYouli"
                   protocol="dubbo" dynamic="true" />
    <bean id="iVideoYouli" class="com.heytap.video.youli.service.impl.IVideoYouliImpl" />
</beans>