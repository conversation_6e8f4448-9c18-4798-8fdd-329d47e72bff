#三方源api配置信息
configMap:
  #搜索api配置
  search:
    #    url: 'http://api.searchcloud.oppoer.me/search/v1/video'
    url: 'http://10.176.162.177:48812/search/v1/video'
    method: 'GET'
    timeout: 1000

  #资源中台api配置
  resource:
    #    url: 'https://content.cc.heytapmobi.com/resource/issued/resource'
    url: 'http://10.176.162.177:48812/resource/issued/resource'
    headers: {
      appId: '2',
      Content-Type: 'application/json;charset=utf-8'
    }
    method: 'POST'
    timeout: 2048

  #搜索中台联想词api配置
  suggestion:
    #    url: 'http://api.searchcloud.oppoer.me/search/v1/sug'
    url: 'http://10.176.162.177:48812/search/v1/sug'

    method: 'GET'
    timeout: 512

  #搜索中台热词词api配置
  hotKeyWords:
    #    url: 'http://api.searchcloud.oppoer.me/search/v1/hot-keywords'
    url: 'http://10.176.162.177:48812/search/v1/hot-keywords'
    method: 'GET'
    timeout: 512

    #长视频搜索api配置
  searchLong:
    #    url: 'https://api.sc.heytapmobi.com/search/v1/vod'
    url: 'http://10.176.162.177:48812/search/v1/vod'
    method: 'GET'
    timeout: 1
    extendsMap: {
      # 搜索的视频最小分值
      minScore: 0.5
    }

  #获取评论数api配置
  commentCount:
    url: 'http://10.176.162.177:48812/comment/queryBatchCommentCount'
    headers: {
      Content-Type: 'application/json;charset=UTF-8'
    }
    method: 'POST'
    timeout: 512
    extendsMap: {
      # 获取评论数开关
      commentSwitch: true
    }
  #中台内容点赞更新api配置
  resourceUpdate:
    url: 'http://gank.search-test.wanyol.com/resource/aggregation/dyn/resourceDynUpdate'
    headers: {
      appId: '2',
      Content-Type: 'application/json;charset=utf-8'
    }
    method: 'POST'
    timeout: 1024
