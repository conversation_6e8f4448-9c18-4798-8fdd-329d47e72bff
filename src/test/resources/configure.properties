zookeeper.url=*************:2181,*************:2181,*************:2181
env=test
file_cache=file-cache1/
monitor_url=http://***********:28083
fullBrowserVersion=4.1

feeds_access=http://ivideo.test-browser.wanyol.com
oss_access=https://youlishipin-com.oss-cn-beijing.aliyuncs.com,http://youlishipin-com.oss-cn-beijing.aliyuncs.com
oss_access_img=https://img-cdn-youlishipin-com.oss-cn-beijing.aliyuncs.com,http://img-cdn-youlishipin-com.oss-cn-beijing.aliyuncs.com
cdn_access_img=https://img-cdn.youlishipin.com

dubbo.threads=256
dubbo.route.mode=all

#ç®æ³ååºæ´æ°å¼å³
algorithm.cache.updateSwitch=true
#ç®æ³ååºèç®å¤§å°
algorithm.cache.maxSize=100
#ç®æ³ååºè¿ææ¶é´
algorithm.cache.expiredMin=10080
#ç®æ³ååºæ´æ°æ¶é´
algorithm.cache.updateMin=60
#ç®æ³ååº - redisèæ¬ï¼å¤æ­æ¯å¦éè¦æ´æ°ç¼å­
algorithm.cache.needUpdateScript=local count = redis.call('ZCARD', KEYS[1]) local minScore = redis.call('ZRANGE', KEYS[1], 0, 0, 'WITHSCORES')[2] if count < tonumber(ARGV[1]) or (minScore and tonumber(minScore) < tonumber(ARGV[2])) then return 1 else return 0 end
#ç®æ³ååº - redisèæ¬ï¼å é¤æº¢åºæ°æ®
algorithm.cache.delScript=local count = redis.call('ZCARD', KEYS[1]) if count > tonumber(ARGV[1]) then redis.call('ZREMRANGEBYRANK', KEYS[1], 0, count-tonumber(ARGV[1])-1) end