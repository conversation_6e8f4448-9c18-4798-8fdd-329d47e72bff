rocketmq.consumer.namesrvAddr=10.177.93.73:9876;10.177.93.74:9876;10.177.93.72:9876

rocketmq.consumer.videolog.consumerGroup=video-youli-service-consumer-group
rocketmq.consumer.orderly.consumerGroup=video-youli-service-orderly-consumer-group
rocketmq.consumer.video.hotsoon.topic=video-log-service_hotsoon_video_topic_20191025
rocketmq.consumer.video.horizon.topic=video-log-service_horizon_video_topic_20191025
rocketmq.consumer.thumbUp.favorite.topic=video-interact-service_thumbUp_topic_20191031
rocketmq.consumer.thumbUp.favorite.new.topic=video-interact-service_thumbUp_topic_20191031
rocketmq.consumer.videolog.subExpression=youli

rocketmq.consumer.consumeThreadMin=16
rocketmq.consumer.consumeThreadMax=64
rocketmq.consumer.pullInterval=0
rocketmq.consumer.consumeMessageBatchMaxSize=1
rocketmq.consumer.maxReconsumeTimes=2
rocketmq.consumer.clientCallbackExecutorThreads=2
rocketmq.consumer.pollNameServerInterval=30000
rocketmq.consumer.heartbeatBrokerInterval=30000
rocketmq.consumer.persistConsumerOffsetInterval=5000

rocketmq.consumer.orderly.consumeThreadMin=16
rocketmq.consumer.orderly.consumeThreadMax=64
rocketmq.consumer.orderly.pullInterval=0
rocketmq.consumer.orderly.consumeMessageBatchMaxSize=1
rocketmq.consumer.orderly.maxReconsumeTimes=2
rocketmq.consumer.orderly.clientCallbackExecutorThreads=2
rocketmq.consumer.orderly.pollNameServerInterval=30000
rocketmq.consumer.orderly.heartbeatBrokerInterval=30000
rocketmq.consumer.orderly.persistConsumerOffsetInterval=5000