user-history = {
  redis-client-bjht = {
    cluster-nodes = [
      "10.177.85.181:6379",
      "10.177.85.180:6379",
      "10.177.85.184:6379",
      "10.177.85.186:6379",
      "10.177.85.185:6379",
      "10.177.85.187:6379",
      "10.177.85.182:6379",
      "10.177.85.183:6379",
      "10.177.85.188:6379",
      "10.177.85.189:6379"
    ]

    pool-config = {
      maxTotal = 100
      maxIdle = 10
      minIdle = 10
      maxWaitMillis = 100
      testOnBorrow = true
      testWhileIdle = true
      timeBetweenEvictionRunsMillis = 1000
      minEvictableIdleTimeMillis = 1000
      numTestsPerEvictionRun = 1
    }


    connectTimeout = 100
    soTimeout = 200
    maxAttempts = 3
  }

  redis-client-bjcp = {
    cluster-nodes = [
      "10.177.85.181:6379",
      "10.177.85.180:6379",
      "10.177.85.184:6379",
      "10.177.85.186:6379",
      "10.177.85.185:6379",
      "10.177.85.187:6379",
      "10.177.85.182:6379",
      "10.177.85.183:6379",
      "10.177.85.188:6379",
      "10.177.85.189:6379"
    ]

    pool-config = {
      maxTotal = 100
      maxIdle = 10
      minIdle = 10
      maxWaitMillis = 100
      testOnBorrow = true
      testWhileIdle = true
      timeBetweenEvictionRunsMillis = 1000
      minEvictableIdleTimeMillis = 1000
      numTestsPerEvictionRun = 1
    }


    connectTimeout = 100
    soTimeout = 200
    maxAttempts = 3
  }

  maxListSize = 5000
  expireInSeconds = 5184000
  keyPrefix="pgc:history:uservideo:"
}