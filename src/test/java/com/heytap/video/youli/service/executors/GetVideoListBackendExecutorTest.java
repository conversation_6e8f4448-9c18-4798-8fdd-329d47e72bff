package com.heytap.video.youli.service.executors;

import com.heytap.video.youli.builder.request.FeedsListReqPropertyBuilder;
import com.heytap.video.youli.builder.response.QueryBatchCommentCountResponseBuilder;
import com.heytap.video.youli.builder.response.ResourcePondResourceResponseBuilder;
import com.heytap.video.youli.util.MySpringJUnit4ClassRunner;
import com.oppo.browser.video.common.pubobj.resultObj.news.VideoFeedsListRt;
import esa.rpc.dubbo.rpc.mock.config.Mocker;
import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockserver.integration.ClientAndServer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.assertNotNull;
import static org.mockserver.model.HttpRequest.request;
import static org.mockserver.model.HttpResponse.response;

@RunWith(MySpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:test-application-context.xml"})
public class GetVideoListBackendExecutorTest {

    @Autowired
    private GetVideoListBackendExecutor getVideoListBackendExecutor;

    private static ClientAndServer mockServer;

    @BeforeClass
    public static void startMockServer() {
        mockServer = ClientAndServer.startClientAndServer(1080);
        Mocker.startMock();
    }

    @AfterClass
    public static void stopMockServer() {
        mockServer.stop();
        Mocker.stopMock();
    }

    @Test
    public void test() throws Exception {
        mockServer.when(request().withPath("/resource/pond/resource")).respond(response().withBody(ResourcePondResourceResponseBuilder.build()));
        mockServer.when(request().withPath("/comment/queryBatchCommentCount")).respond(response().withBody(QueryBatchCommentCountResponseBuilder.build()));

        CompletableFuture<VideoFeedsListRt> rtCf = getVideoListBackendExecutor.execute(new FeedsListReqPropertyBuilder().build());
        VideoFeedsListRt rt = rtCf.get();
        assertNotNull(rt);
    }

}
