package com.heytap.video.youli.service.executors;

import com.heytap.video.youli.builder.request.BatchArticleInfoReqPropertyBuilder;
import com.heytap.video.youli.builder.response.QueryBatchCommentCountResponseBuilder;
import com.heytap.video.youli.builder.response.ResourceIssuedResourceBuilder;
import com.heytap.video.youli.util.MySpringJUnit4ClassRunner;
import com.oppo.browser.video.common.pubobj.resultObj.news.VideoArticleMapRt;
import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockserver.integration.ClientAndServer;
import org.mockserver.model.ClearType;
import org.mockserver.model.HttpRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockserver.model.HttpRequest.request;
import static org.mockserver.model.HttpResponse.response;

@RunWith(MySpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:test-application-context.xml"})
public class GetBatchVideoInfoBackendExecutorTest {

    @Autowired
    private GetBatchVideoInfoBackendExecutor getBatchVideoInfoBackendExecutor;

    private static ClientAndServer mockServer;

    private HttpRequest resourceRequest = request().withPath("/resource/issued/resource");
    private HttpRequest commentRequest = request().withPath("/comment/queryBatchCommentCount");

    @BeforeClass
    public static void startMockServer() {
        mockServer = ClientAndServer.startClientAndServer(1080);
    }

    @AfterClass
    public static void stopMockServer() {
        mockServer.stop();
    }

    @Test
    public void case1() throws Exception{
        mockServer.when(resourceRequest).respond(response().withBody(ResourceIssuedResourceBuilder.buildRsp()));
        mockServer.when(commentRequest).respond(response().withBody(QueryBatchCommentCountResponseBuilder.build()));
        CompletableFuture<VideoArticleMapRt> rtCf = getBatchVideoInfoBackendExecutor.execute(BatchArticleInfoReqPropertyBuilder.build());
        VideoArticleMapRt rt = rtCf.get();
        assertNotNull(rt);
        assertNotNull(rt.getData());
        assertTrue(rt.getRet() == 0);
        mockServer.clear(resourceRequest, ClearType.ALL);
        mockServer.clear(commentRequest, ClearType.ALL);
    }

    @Test
    public void case2() throws Exception{
        CompletableFuture<VideoArticleMapRt> rtCf = getBatchVideoInfoBackendExecutor.execute(BatchArticleInfoReqPropertyBuilder.build());
        VideoArticleMapRt rt = rtCf.get();
        assertNotNull(rt);
        assertTrue(rt.getRet() == 1400);
    }

    @Test
    public void case3() throws Exception{
        mockServer.when(resourceRequest).respond(response().withBody(ResourceIssuedResourceBuilder.buildRsp()));
        CompletableFuture<VideoArticleMapRt> rtCf = getBatchVideoInfoBackendExecutor.execute(BatchArticleInfoReqPropertyBuilder.build());
        VideoArticleMapRt rt = rtCf.get();
        assertNotNull(rt);
        assertNotNull(rt.getData());
        assertTrue(rt.getRet() == 0);
        mockServer.clear(resourceRequest, ClearType.ALL);
    }

}
