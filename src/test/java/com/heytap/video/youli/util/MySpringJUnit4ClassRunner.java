package com.heytap.video.youli.util;

import org.junit.Rule;
import org.junit.contrib.java.lang.system.EnvironmentVariables;
import org.junit.runners.model.InitializationError;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * Description:
 *
 * <AUTHOR> 80237102
 * @date 2021/8/10 10:46
 */
public class MySpringJUnit4ClassRunner extends SpringJUnit4ClassRunner {

    @Rule
    public final EnvironmentVariables environmentVariables = new EnvironmentVariables();

    public MySpringJUnit4ClassRunner(Class<?> clazz) throws InitializationError {
        super(clazz);
        environmentVariables.set("heracles_app_id", "video-youli-service");
    }
}
