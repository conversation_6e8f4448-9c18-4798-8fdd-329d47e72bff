package com.heytap.video.youli.biz.algorithm;

import com.heytap.video.youli.builder.request.ListBaseReqPropertyBuilder;
import com.heytap.video.youli.config.YouliApiConfig;
import com.heytap.video.youli.utils.BizUtils;
import com.oppo.browser.common.app.lib.cookie.Cookie;
import com.oppo.browser.common.app.lib.cookie.UserInfo;
import com.oppo.browser.common.app.lib.encryption.JsonTools;
import com.oppo.browser.common.app.lib.utils.Env;
import com.oppo.browser.strategy.model.AttributeValues;
import com.oppo.browser.video.common.app.lib.strategy.IPLocationStrategyResult;
import com.oppo.browser.video.common.pubobj.constant.RecTypeConstant;
import com.oppo.browser.video.common.pubobj.resource.ListBaseReqProperty;
import com.oppo.trace.core.TraceThreadLocal;
import com.oppo.trace.core.scope.TraceScope;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import static com.heytap.video.youli.constant.GlobalConstant.APPID;
import static com.heytap.video.youli.constant.GlobalConstant.R_ACTION;
import static com.heytap.video.youli.constant.GlobalConstant.R_PAGE;
import static com.heytap.video.youli.constant.GlobalConstant.TRACEID;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

/**
 * RecommendService 单元测试类
 *
 * <p>主要测试 {@link RecommendService#getRecReqParams(ListBaseReqProperty)} 方法及其重构后的子方法。
 * 该测试类验证了重构后代码的功能完整性、边界条件处理和异常情况处理。</p>
 *
 * <p>重构前的 getRecReqParams 方法圈复杂度为 24，重构后降低到 6 以下，
 * 通过方法分解、条件提取、逻辑分层等技术手段提升了代码的可读性和可维护性。</p>
 *
 * <h3>测试覆盖范围：</h3>
 * <ul>
 *   <li>主方法功能测试：正常情况、边界条件、异常情况</li>
 *   <li>重构后子方法测试：每个私有方法的独立测试</li>
 *   <li>参数验证：必传参数和可选参数的正确性验证</li>
 *   <li>环境差异测试：不同环境下的参数差异验证</li>
 * </ul>
 *
 * <h3>Mock 策略：</h3>
 * <ul>
 *   <li>使用 PowerMock 来 Mock 静态方法（Env、TraceThreadLocal、JsonTools、BizUtils）</li>
 *   <li>使用 Mockito 来 Mock 复杂对象（Cookie、YouliApiConfig）</li>
 *   <li>使用 ListBaseReqPropertyBuilder 来构建测试数据</li>
 * </ul>
 *
 * <AUTHOR> Assistant
 * @version 1.0
 * @since 2025-01-01
 * @see RecommendService
 * @see ListBaseReqPropertyBuilder
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({Env.class, TraceThreadLocal.class, TraceScope.class, BizUtils.class, JsonTools.class})
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
public class RecommendServiceTest {

    /**
     * 被测试的服务类实例，通过 @InjectMocks 自动注入 Mock 依赖
     */
    @InjectMocks
    private RecommendService recommendService;

    /**
     * Mock 的配置类，用于提供测试所需的配置参数
     */
    @Mock
    private YouliApiConfig youliApiConfig;

    /**
     * 测试用的请求属性对象
     */
    private ListBaseReqProperty reqProperty;

    /**
     * 测试用的属性值对象，从 reqProperty 中获取
     */
    private AttributeValues attributeValues;

    /**
     * 测试前的初始化方法
     *
     * <p>设置测试所需的 Mock 对象和测试数据，包括：</p>
     * <ul>
     *   <li>初始化测试用的 ListBaseReqProperty 对象</li>
     *   <li>Mock YouliApiConfig 的配置方法</li>
     *   <li>Mock BizUtils 的静态方法</li>
     * </ul>
     */
    @Before
    public void setUp() {
        // 初始化测试数据
        setupTestData();

        // Mock YouliApiConfig
        when(youliApiConfig.getVideoRecLimit()).thenReturn(20);
        when(youliApiConfig.getVideoRec2NLimit()).thenReturn(4);
        PowerMockito.mockStatic(BizUtils.class);
        when(BizUtils.getRelevantBidLst(any(YouliApiConfig.class))).thenReturn("test_bidlst");
    }

    /**
     * 设置测试数据
     *
     * <p>使用 {@link ListBaseReqPropertyBuilder} 构建标准的测试数据，
     * 包含所有必要的字段和合理的默认值。</p>
     *
     * <p>测试数据特点：</p>
     * <ul>
     *   <li>包含完整的用户属性信息</li>
     *   <li>包含有效的 Cookie 信息</li>
     *   <li>包含地理位置信息</li>
     *   <li>包含设备和网络信息</li>
     * </ul>
     */
    private void setupTestData() {
        reqProperty = new ListBaseReqPropertyBuilder()
                .withRequestId("test_request_id")
                .withDetailStyle(1)
                .withRecType("normal")
                .withFromId("test_from_id")
                .withOffset(0)
                .withDownTimes(1)
                .withUpTimes(0)
                .withArea("{\"country\":\"China\",\"province\":\"Beijing\",\"city\":\"Beijing\"}")
                .withDocid("test_docid")
                .withPageID("test_page_id")
                .withSpageID("test_spage_id")
                .withMediaNo("test_media_no")
                .withNetwork("wifi")
                .withPhone("OPPO_R15")
                .withClientVersion("4.1.0")
                .withIp("***********")
                .withBuuid(123456789L)
                .withImei("test_imei")
                .withUsername("test_username")
                .withCookieBuuid(987654321L)
                .build();

        attributeValues = reqProperty.getAttributeValues();
    }

    /**
     * 测试 getRecReqParams 方法的正常情况
     *
     * <p>验证在正常输入参数下，方法能够正确构建推荐请求参数。
     * 这是最重要的测试用例，验证了重构后方法的核心功能。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>生产环境（不添加路由参数）</li>
     *   <li>完整的用户属性信息</li>
     *   <li>有效的地理位置信息</li>
     *   <li>正常的详情样式（detailStyle=1）</li>
     *   <li>下拉刷新（offset=0）</li>
     * </ul>
     *
     * <h3>验证点：</h3>
     * <ul>
     *   <li>必传参数：appId、cid、time、traceId、num、bidlst 等</li>
     *   <li>频道参数：r_channel_id、r_channel_type</li>
     *   <li>刷新参数：r_action、r_page、r_refresh</li>
     *   <li>地理位置参数：r_area</li>
     *   <li>设备参数：network、r_devtype、r_client_version、ip</li>
     *   <li>用户参数：personalRec、r_username</li>
     *   <li>扩展参数：needRelation、picFormatType、r_used、r_relatedId、r_cardtype、recommendExt、authorId</li>
     * </ul>
     *
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testGetRecReqParams_NormalCase() throws Exception {
        // Mock 环境
        PowerMockito.mockStatic(Env.class);
        when(Env.getEnv()).thenReturn("prod");

        // Mock JsonTools
        PowerMockito.mockStatic(JsonTools.class);
        IPLocationStrategyResult location = new IPLocationStrategyResult();
        location.setCountry("China");
        location.setProvince("Beijing");
        location.setCity("Beijing");
        when(JsonTools.toMap(any(String.class), any(Class.class))).thenReturn(location);

        // 调用私有方法
        Method method = RecommendService.class.getDeclaredMethod("getRecReqParams", ListBaseReqProperty.class);
        method.setAccessible(true);
        Map<String, Object> result = (Map<String, Object>) method.invoke(recommendService, reqProperty);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.get(APPID));
        Assert.assertEquals("video", result.get("cid"));
        Assert.assertEquals("test_request_id", result.get(TRACEID));
        Assert.assertEquals(20, result.get("num"));
        Assert.assertEquals("test_bidlst", result.get("bidlst"));
        Assert.assertEquals("test_from_id", result.get("r_channel_id"));
        Assert.assertEquals("video", result.get("r_channel_type"));
        Assert.assertEquals(0, result.get(R_ACTION));
        Assert.assertEquals(1, result.get(R_PAGE));
        Assert.assertEquals(1, result.get("r_refresh"));
        Assert.assertEquals("country:China;province:Beijing;city:Beijing", result.get("r_area"));
        Assert.assertEquals("wifi", result.get("network"));
        Assert.assertEquals("OPPO_R15", result.get("r_devtype"));
        Assert.assertEquals("4.1.0", result.get("r_client_version"));
        Assert.assertEquals("***********", result.get("ip"));
        Assert.assertEquals(1, result.get("personalRec"));
        Assert.assertEquals("test_username", result.get("r_username"));
        Assert.assertEquals(false, result.get("needRelation"));
        Assert.assertEquals(1, result.get("picFormatType"));
        Assert.assertEquals("0", result.get("r_used"));
        Assert.assertEquals("test_docid", result.get("r_relatedId"));
        Assert.assertEquals("VIDEO", result.get("r_cardtype"));
        Assert.assertEquals("parFromId=test_spage_id", result.get("recommendExt"));
        Assert.assertEquals("test_media_no", result.get("authorId"));
    }

    /**
     * 测试测试环境下的路由参数
     *
     * <p>验证在测试环境（test 或 env）下，方法会自动添加路由参数 "bjhtonlinerec"。
     * 这是环境相关的重要功能，确保测试环境的请求能够正确路由。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>环境变量为 "test"</li>
     *   <li>其他参数保持默认</li>
     * </ul>
     *
     * <h3>验证点：</h3>
     * <ul>
     *   <li>route 参数存在且值为 "bjhtonlinerec"</li>
     * </ul>
     *
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testGetRecReqParams_TestEnvironment() throws Exception {
        // Mock 测试环境
        PowerMockito.mockStatic(Env.class);
        when(Env.getEnv()).thenReturn("test");

        Method method = RecommendService.class.getDeclaredMethod("getRecReqParams", ListBaseReqProperty.class);
        method.setAccessible(true);
        Map<String, Object> result = (Map<String, Object>) method.invoke(recommendService, reqProperty);

        // 验证测试环境下的路由参数
        Assert.assertEquals("bjhtonlinerec", result.get("route"));
    }

    /**
     * 测试 2N 详情页的推荐数量
     *
     * <p>验证当 detailStyle = 3 时，推荐数量应该使用 2N 详情页的限制（4个）
     * 而不是默认的推荐数量（20个）。这是业务逻辑的重要分支。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>detailStyle = 3（2N 详情页）</li>
     *   <li>其他参数保持默认</li>
     * </ul>
     *
     * <h3>验证点：</h3>
     * <ul>
     *   <li>num 参数值为 4（而不是默认的 20）</li>
     * </ul>
     *
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testGetRecReqParams_DetailStyle3() throws Exception {
        // 使用 Builder 创建 detailStyle = 3 的测试数据
        ListBaseReqProperty testReqProperty = new ListBaseReqPropertyBuilder()
                .withDetailStyle(3)
                .build();

        PowerMockito.mockStatic(Env.class);
        when(Env.getEnv()).thenReturn("prod");

        Method method = RecommendService.class.getDeclaredMethod("getRecReqParams", ListBaseReqProperty.class);
        method.setAccessible(true);
        Map<String, Object> result = (Map<String, Object>) method.invoke(recommendService, testReqProperty);

        // 验证 2N 详情页的推荐数量
        Assert.assertEquals(4, result.get("num"));
    }

    @Test
    public void testGetRecReqParams_H5RecType() throws Exception {
        // 使用 Builder 创建 H5 类型的测试数据
        ListBaseReqProperty testReqProperty = new ListBaseReqPropertyBuilder()
                .withRecType(RecTypeConstant.H5)
                .build();

        PowerMockito.mockStatic(Env.class);
        when(Env.getEnv()).thenReturn("prod");

        Method method = RecommendService.class.getDeclaredMethod("getRecReqParams", ListBaseReqProperty.class);
        method.setAccessible(true);
        Map<String, Object> result = (Map<String, Object>) method.invoke(recommendService, testReqProperty);

        // 验证结果包含基本参数（H5 参数处理在 appendH5Params 中，这里主要验证方法调用）
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.get(APPID));
    }

    @Test
    public void testGetRecReqParams_OffsetNotZero() throws Exception {
        // 设置 offset 不为 0
        reqProperty.setOffset(10);
        reqProperty.setUpTimes(2);

        PowerMockito.mockStatic(Env.class);
        when(Env.getEnv()).thenReturn("prod");

        Method method = RecommendService.class.getDeclaredMethod("getRecReqParams", ListBaseReqProperty.class);
        method.setAccessible(true);
        Map<String, Object> result = (Map<String, Object>) method.invoke(recommendService, reqProperty);

        // 验证翻页参数
        Assert.assertEquals(1, result.get(R_ACTION));
        Assert.assertEquals(2, result.get(R_PAGE));
    }

    @Test
    public void testGetRecReqParams_PushDetail2N() throws Exception {
        // 设置 push_detail_2N 页面
        reqProperty.setPageID("push_detail_2N");

        PowerMockito.mockStatic(Env.class);
        when(Env.getEnv()).thenReturn("prod");

        Method method = RecommendService.class.getDeclaredMethod("getRecReqParams", ListBaseReqProperty.class);
        method.setAccessible(true);
        Map<String, Object> result = (Map<String, Object>) method.invoke(recommendService, reqProperty);

        // 验证推荐扩展参数
        Assert.assertEquals("parFromId=push_click", result.get("recommendExt"));
    }

    @Test
    public void testGetRecReqParams_NoRequestId_WithTraceScope() throws Exception {
        // 清空 requestId
        reqProperty.setRequestId(null);

        // Mock TraceThreadLocal
        PowerMockito.mockStatic(TraceThreadLocal.class);
        TraceScope traceScope = PowerMockito.mock(TraceScope.class);
        Map<String, String> attachments = new HashMap<>();
        attachments.put(TraceScope.ATTACHMENT_KEY.REQUEST_ID, "trace_request_id");
        
        when(TraceThreadLocal.getScope()).thenReturn(traceScope);
        when(traceScope.getAttachments()).thenReturn(attachments);

        PowerMockito.mockStatic(Env.class);
        when(Env.getEnv()).thenReturn("prod");

        Method method = RecommendService.class.getDeclaredMethod("getRecReqParams", ListBaseReqProperty.class);
        method.setAccessible(true);
        Map<String, Object> result = (Map<String, Object>) method.invoke(recommendService, reqProperty);

        // 验证从 TraceScope 获取的 traceId
        Assert.assertEquals("trace_request_id", result.get(TRACEID));
    }

    @Test
    public void testGetRecReqParams_NoRequestId_NoTraceScope() throws Exception {
        // 清空 requestId
        reqProperty.setRequestId(null);

        // Mock TraceThreadLocal 返回 null
        PowerMockito.mockStatic(TraceThreadLocal.class);
        when(TraceThreadLocal.getScope()).thenReturn(null);

        PowerMockito.mockStatic(Env.class);
        when(Env.getEnv()).thenReturn("prod");

        Method method = RecommendService.class.getDeclaredMethod("getRecReqParams", ListBaseReqProperty.class);
        method.setAccessible(true);
        Map<String, Object> result = (Map<String, Object>) method.invoke(recommendService, reqProperty);

        // 验证生成了 UUID 作为 traceId
        String traceId = (String) result.get(TRACEID);
        Assert.assertNotNull(traceId);
        Assert.assertTrue(traceId.length() > 0);
        Assert.assertFalse(traceId.contains("-")); // UUID 中的 "-" 应该被替换掉
    }

    @Test
    public void testGetRecReqParams_EmptyOptionalFields() throws Exception {
        // 使用 Builder 创建可选字段为空的测试数据
        ListBaseReqProperty testReqProperty = new ListBaseReqPropertyBuilder()
                .withNullArea()
                .withNullMediaNo()
                .withNullNetwork()
                .withNullPhone()
                .withNullClientVersion()
                .withNullIp()
                .withNullCookie()
                .build();

        PowerMockito.mockStatic(Env.class);
        when(Env.getEnv()).thenReturn("prod");

        Method method = RecommendService.class.getDeclaredMethod("getRecReqParams", ListBaseReqProperty.class);
        method.setAccessible(true);
        Map<String, Object> result = (Map<String, Object>) method.invoke(recommendService, testReqProperty);

        // 验证可选字段不存在
        Assert.assertFalse(result.containsKey("r_area"));
        Assert.assertFalse(result.containsKey("network"));
        Assert.assertFalse(result.containsKey("r_devtype"));
        Assert.assertFalse(result.containsKey("r_client_version"));
        Assert.assertFalse(result.containsKey("ip"));
        Assert.assertFalse(result.containsKey("r_username"));
        Assert.assertFalse(result.containsKey("authorId"));

        // 验证必传字段仍然存在
        Assert.assertEquals(2, result.get(APPID));
        Assert.assertEquals("video", result.get("cid"));
        Assert.assertNotNull(result.get(TRACEID));
    }

    // ========== 测试重构后的子方法 ==========

    /**
     * 测试环境判断方法
     *
     * <p>验证 {@code isTestEnvironment()} 方法能够正确判断当前是否为测试环境。
     * 该方法是重构后提取的私有方法，用于简化环境判断逻辑。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>环境变量为 "test" - 应返回 true</li>
     *   <li>环境变量为 "env" - 应返回 true</li>
     *   <li>环境变量为 "prod" - 应返回 false</li>
     * </ul>
     *
     * <h3>重构效果：</h3>
     * <ul>
     *   <li>将复杂的 || 条件判断提取为独立方法</li>
     *   <li>提高了代码的可读性和可测试性</li>
     *   <li>降低了主方法的圈复杂度</li>
     * </ul>
     *
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testIsTestEnvironment() throws Exception {
        PowerMockito.mockStatic(Env.class);

        // 测试 test 环境
        when(Env.getEnv()).thenReturn("test");
        Method method = RecommendService.class.getDeclaredMethod("isTestEnvironment");
        method.setAccessible(true);
        Boolean result = (Boolean) method.invoke(recommendService);
        Assert.assertTrue(result);

        // 测试 env 环境
        when(Env.getEnv()).thenReturn("env");
        result = (Boolean) method.invoke(recommendService);
        Assert.assertTrue(result);

        // 测试生产环境
        when(Env.getEnv()).thenReturn("prod");
        result = (Boolean) method.invoke(recommendService);
        Assert.assertFalse(result);
    }

    /**
     * 测试推荐数量确定方法
     *
     * <p>验证 {@code determineRecommendNum()} 方法能够根据详情样式正确确定推荐数量。
     * 该方法是重构后提取的私有方法，用于简化推荐数量的判断逻辑。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>detailStyle = 3（2N详情页）- 应返回 4</li>
     *   <li>detailStyle = 1（普通详情页）- 应返回 20</li>
     * </ul>
     *
     * <h3>业务逻辑：</h3>
     * <ul>
     *   <li>2N详情页需要限制推荐数量以优化用户体验</li>
     *   <li>普通详情页使用默认的推荐数量</li>
     * </ul>
     *
     * <h3>重构效果：</h3>
     * <ul>
     *   <li>将条件判断逻辑提取为独立方法</li>
     *   <li>提高了代码的可读性和可维护性</li>
     *   <li>便于单独测试业务逻辑</li>
     * </ul>
     *
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testDetermineRecommendNum() throws Exception {
        Method method = RecommendService.class.getDeclaredMethod("determineRecommendNum", ListBaseReqProperty.class);
        method.setAccessible(true);

        // 测试 detailStyle = 3
        reqProperty.setDetailStyle(3);
        Integer result = (Integer) method.invoke(recommendService, reqProperty);
        Assert.assertEquals(Integer.valueOf(4), result);

        // 测试其他 detailStyle
        reqProperty.setDetailStyle(1);
        result = (Integer) method.invoke(recommendService, reqProperty);
        Assert.assertEquals(Integer.valueOf(20), result);
    }

    @Test
    public void testExtractTraceId() throws Exception {
        Method method = RecommendService.class.getDeclaredMethod("extractTraceId", ListBaseReqProperty.class);
        method.setAccessible(true);

        // 测试有 requestId 的情况
        reqProperty.setRequestId("test_request_id");
        String result = (String) method.invoke(recommendService, reqProperty);
        Assert.assertEquals("test_request_id", result);

        // 测试没有 requestId 但有 TraceScope 的情况
        reqProperty.setRequestId(null);
        PowerMockito.mockStatic(TraceThreadLocal.class);
        TraceScope traceScope = PowerMockito.mock(TraceScope.class);
        Map<String, String> attachments = new HashMap<>();
        attachments.put(TraceScope.ATTACHMENT_KEY.REQUEST_ID, "trace_id");
        when(TraceThreadLocal.getScope()).thenReturn(traceScope);
        when(traceScope.getAttachments()).thenReturn(attachments);

        result = (String) method.invoke(recommendService, reqProperty);
        Assert.assertEquals("trace_id", result);

        // 测试都没有的情况，应该生成 UUID
        when(TraceThreadLocal.getScope()).thenReturn(null);
        result = (String) method.invoke(recommendService, reqProperty);
        Assert.assertNotNull(result);
        Assert.assertTrue(result.length() > 0);
        Assert.assertFalse(result.contains("-"));
    }

    /**
     * 测试链路追踪上下文有效性验证方法
     *
     * <p>验证 {@code isTraceScopeValid()} 方法能够正确判断链路追踪上下文的有效性。
     * 该方法是重构后提取的私有方法，用于简化复杂的 && 条件判断。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>TraceScope 为 null - 应返回 false</li>
     *   <li>TraceScope 不为 null 但 attachments 为 null - 应返回 false</li>
     *   <li>TraceScope 和 attachments 都不为 null - 应返回 true</li>
     * </ul>
     *
     * <h3>重构效果：</h3>
     * <ul>
     *   <li>将复杂的 && 条件判断提取为独立方法</li>
     *   <li>提高了代码的可读性和可测试性</li>
     *   <li>降低了主方法的圈复杂度</li>
     * </ul>
     *
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testIsTraceScopeValid() throws Exception {
        Method method = RecommendService.class.getDeclaredMethod("isTraceScopeValid");
        method.setAccessible(true);

        PowerMockito.mockStatic(TraceThreadLocal.class);

        // 测试 scope 为 null
        when(TraceThreadLocal.getScope()).thenReturn(null);
        Boolean result = (Boolean) method.invoke(recommendService);
        Assert.assertFalse(result);

        // 测试 scope 不为 null 但 attachments 为 null
        TraceScope traceScope = PowerMockito.mock(TraceScope.class);
        when(TraceThreadLocal.getScope()).thenReturn(traceScope);
        when(traceScope.getAttachments()).thenReturn(null);
        result = (Boolean) method.invoke(recommendService);
        Assert.assertFalse(result);

        // 测试都不为 null
        Map<String, String> attachments = new HashMap<>();
        when(traceScope.getAttachments()).thenReturn(attachments);
        result = (Boolean) method.invoke(recommendService);
        Assert.assertTrue(result);
    }

    /**
     * 测试Cookie用户名有效性验证方法
     *
     * <p>验证 {@code isCookieUsernameValid()} 方法能够正确判断Cookie中用户名的有效性。
     * 该方法是重构后提取的私有方法，用于简化复杂的 && 条件判断。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>Cookie 为 null - 应返回 false</li>
     *   <li>Cookie 不为 null 但 info 为 null - 应返回 false</li>
     *   <li>info 不为 null 但 username 为空 - 应返回 false</li>
     *   <li>所有条件都满足 - 应返回 true</li>
     * </ul>
     *
     * <h3>重构效果：</h3>
     * <ul>
     *   <li>将复杂的 && 条件判断提取为独立方法</li>
     *   <li>提高了代码的可读性和可测试性</li>
     *   <li>降低了主方法的圈复杂度</li>
     * </ul>
     *
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testIsCookieUsernameValid() throws Exception {
        Method method = RecommendService.class.getDeclaredMethod("isCookieUsernameValid", ListBaseReqProperty.class);
        method.setAccessible(true);

        // 测试 cookie 为 null
        reqProperty.setScookie(null);
        Boolean result = (Boolean) method.invoke(recommendService, reqProperty);
        Assert.assertFalse(result);

        // 测试 cookie 不为 null 但 info 为 null
        Cookie cookie = PowerMockito.mock(Cookie.class);
        when(cookie.getInfo()).thenReturn(null);
        reqProperty.setScookie(cookie);
        result = (Boolean) method.invoke(recommendService, reqProperty);
        Assert.assertFalse(result);

        // 测试 info 不为 null 但 username 为空
        UserInfo cookieInfo = PowerMockito.mock(UserInfo.class);
        when(cookie.getInfo()).thenReturn(cookieInfo);
        when(cookieInfo.getUn()).thenReturn("");
        result = (Boolean) method.invoke(recommendService, reqProperty);
        Assert.assertFalse(result);

        // 测试都有效
        when(cookieInfo.getUn()).thenReturn("test_user");
        result = (Boolean) method.invoke(recommendService, reqProperty);
        Assert.assertTrue(result);
    }
    /**
     * 测试用户名提取方法
     *
     * <p>验证 {@code extractUsername()} 方法能够正确从Cookie中提取用户名。
     * 该方法是重构后提取的私有方法，用于简化用户名获取逻辑。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>有效的Cookie用户名 - 应返回正确的用户名</li>
     *   <li>无效的Cookie - 应返回 null</li>
     * </ul>
     *
     * <h3>重构效果：</h3>
     * <ul>
     *   <li>将用户名提取逻辑独立为方法</li>
     *   <li>提高了代码的可读性和可维护性</li>
     *   <li>便于单独测试用户名提取逻辑</li>
     * </ul>
     *
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testExtractUsername() throws Exception {
        Method method = RecommendService.class.getDeclaredMethod("extractUsername", ListBaseReqProperty.class);
        method.setAccessible(true);

        // 测试有效的 cookie username
        Cookie cookie = PowerMockito.mock(Cookie.class);
        UserInfo cookieInfo = PowerMockito.mock(UserInfo.class);
        when(cookie.getInfo()).thenReturn(cookieInfo);
        when(cookieInfo.getUn()).thenReturn("test_username");
        reqProperty.setScookie(cookie);

        String result = (String) method.invoke(recommendService, reqProperty);
        Assert.assertEquals("test_username", result);

        // 测试无效的 cookie
        reqProperty.setScookie(null);
        result = (String) method.invoke(recommendService, reqProperty);
        Assert.assertNull(result);
    }

    /**
     * 测试条件参数添加方法
     *
     * <p>验证 {@code addParamIfNotEmpty()} 方法能够正确处理参数的条件添加。
     * 该方法是重构后提取的通用工具方法，用于简化参数添加的条件判断。</p>
     *
     * <h3>测试场景：</h3>
     * <ul>
     *   <li>非空字符串 - 应该添加到参数Map中</li>
     *   <li>空字符串 - 不应该添加到参数Map中</li>
     *   <li>null值 - 不应该添加到参数Map中</li>
     * </ul>
     *
     * <h3>设计目的：</h3>
     * <ul>
     *   <li>避免在参数Map中添加无效的空值</li>
     *   <li>减少重复的条件判断代码</li>
     *   <li>提高代码的一致性和可维护性</li>
     * </ul>
     *
     * <h3>重构效果：</h3>
     * <ul>
     *   <li>消除了多处重复的 StringUtils.isNotEmpty() 判断</li>
     *   <li>提供了统一的参数添加逻辑</li>
     *   <li>降低了主方法的圈复杂度</li>
     * </ul>
     *
     * @throws Exception 反射调用可能抛出的异常
     */
    @Test
    public void testAddParamIfNotEmpty() throws Exception {
        Method method = RecommendService.class.getDeclaredMethod("addParamIfNotEmpty", Map.class, String.class, String.class);
        method.setAccessible(true);

        Map<String, Object> params = new HashMap<>();

        // 测试非空值
        method.invoke(recommendService, params, "key1", "value1");
        Assert.assertEquals("value1", params.get("key1"));

        // 测试空值
        method.invoke(recommendService, params, "key2", "");
        Assert.assertFalse(params.containsKey("key2"));

        // 测试 null 值
        method.invoke(recommendService, params, "key3", null);
        Assert.assertFalse(params.containsKey("key3"));
    }
}