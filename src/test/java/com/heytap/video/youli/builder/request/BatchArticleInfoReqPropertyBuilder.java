package com.heytap.video.youli.builder.request;

import com.oppo.browser.common.app.lib.encryption.JsonTools;
import com.oppo.browser.video.common.pubobj.resource.BatchArticleInfoReqProperty;
import org.apache.commons.io.FileUtils;

import java.net.URL;
import java.nio.charset.StandardCharsets;

public class BatchArticleInfoReqPropertyBuilder {

    public static BatchArticleInfoReqProperty build() {
        URL url = BatchArticleInfoReqPropertyBuilder.class.getClassLoader().getResource("json/BatchArticleInfoReqProperty.json");
        try {
            String jsonStr = FileUtils.readFileToString(FileUtils.toFile(url), StandardCharsets.UTF_8);
            return JsonTools.toMap(jsonStr, BatchArticleInfoReqProperty.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
