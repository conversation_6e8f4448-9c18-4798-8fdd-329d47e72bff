package com.heytap.video.youli.builder.request;

import com.oppo.browser.strategy.model.AttributeValues;
import com.oppo.browser.video.common.pubobj.resource.FeedsListReqProperty;

public class FeedsListReqPropertyBuilder {
    private FeedsListReqProperty reqProperty;

    public FeedsListReqPropertyBuilder() {
        FeedsListReqProperty reqProperty = new FeedsListReqProperty();
        reqProperty.setMethod("");
        reqProperty.setF("pb");
        reqProperty.setKeyword("");
        reqProperty.setFeedssession("eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1dHlwIjoxLCJidWlkIjoyMzAwOTA0NTUzMTkwNTYzODQsImF1ZCI6InZpZGVvIiwidmVyIjoyLCJyYXQiOjE1Njg3Mjc4NTEsInVubSI6Ik9QUE9fNDkxODc4ODYwIiwiaWQiOiI0OTE4Nzg4NjAiLCJleHAiOjE1ODYzMDE1NDYsImRjIjoidGVzdCJ9.DM6Gsv2JfrMCQ0eRhFEV8ntJzVIGFSpGn_ABYkX_SxI");
        reqProperty.setSource("youli");
        reqProperty.setVersion(31);
        reqProperty.setSession("eyJ0b3V0aWFvIjoiYV90PTMwMTU5NDkxMjc0NjE2NDQ4MzI5ODAzODk4OTZjNDE5OzQ5MTg3ODg2MCIsIm9wcG8iOiJvZnM9ZXlKMGVYQWlPaUpLVjFRaUxDSmhiR2NpT2lKSVV6STFOaUo5LmV5SjFkSGx3SWpveExDSmlkV2xrSWpveU16QXdPVEEwTlRVek1Ua3dOVFl6T0RRc0ltRjFaQ0k2SW5acFpHVnZJaXdpZG1WeUlqb3lMQ0p5WVhRaU9qRTFOamczTWpjNE5URXNJblZ1YlNJNklrOVFVRTlmTkRreE9EYzRPRFl3SWl3aWFXUWlPaUkwT1RFNE56ZzROakFpTENKbGVIQWlPakUxT0RZek1ERTFORFlzSW1Saklqb2lkR1Z6ZENKOS5ETTZHc3YySmZyTUNRMGVSaEZFVjhudEp6VklHRlNwR25fQUJZa1hfU3hJIiwicyI6InRvdXRpYW8iLCJpbmZvIjp7InJ0IjoiMTU4NjI3Mjc0NiIsImZ0IjoiMTU2ODcyNzg1MSIsInVuIjoiT1BQT180OTE4Nzg4NjAiLCJ1aWQiOiI0OTE4Nzg4NjAiLCJzaWduIjoiMTg0OUQxRDVCMDg3N0YwNUNERjVFOEEzQzQ1QkEwQjkifX0=");
        reqProperty.setChannelSource("youli");
        reqProperty.setChannelName("--");
        reqProperty.setChannelShortName("--");
        reqProperty.setSearch(false);
        reqProperty.setRecVideo(true);
        reqProperty.setFromId("against_pnuemonia");
        reqProperty.setLimit(10);
        reqProperty.setPage(1);
        reqProperty.setOffset(0);
        reqProperty.setRefresh(1);
        reqProperty.setType("video");
        reqProperty.setRefreshTimes(1);
        reqProperty.setPreAdNumber(0);
        reqProperty.setEnterId(3);
        reqProperty.setIsHomePage(false);
        reqProperty.setOffline(false);
        reqProperty.setPromptRefreshCount(0);
        reqProperty.setSubRefreshTimes(1);
        reqProperty.setIsSubChannel(false);
        reqProperty.setInterestCardNewUser(false);
        reqProperty.setSaveMode(false);
        reqProperty.setIsDown(false);
        reqProperty.setUpTimes(1);
        reqProperty.setDownTimes(0);
        reqProperty.setEnableSportsLive(false);
        reqProperty.setEnableStock(false);
        reqProperty.setFirstLoginTime(1568727851);
        reqProperty.setSearchType(0);
        reqProperty.setDocid("");
        reqProperty.setStatisticsid("");
        reqProperty.setVideoName("");
        reqProperty.setRecType("");

        AttributeValues attributeValues = new AttributeValues();
        attributeValues.setRegion("CN");
        attributeValues.setIp("**************");
        attributeValues.setBrowser("3.1.0");
        attributeValues.setFullBrowserVersion("3.1.0");
        attributeValues.setClientFullBrowserVersion("3.1.0");
        attributeValues.setChannel("OPPO");
        attributeValues.setPhone("PADM00");
        attributeValues.setRom("Android9");
        attributeValues.setOsVersion("V6.0.1");
        attributeValues.setRomVersion("PADM00_11_C.14");
        attributeValues.setResolution("1080*2280");
        attributeValues.setLogic("320*676");
        attributeValues.setNetwork("wifi");
        attributeValues.setImei("867559045441455");
        attributeValues.setBrowserLanguage("zh-CN");
        attributeValues.setSystemLanguage("zh-CN");
        attributeValues.setUuid("6554db34e5f894adbfd3653716973ebd");
        attributeValues.setMp("China Mobie");
        attributeValues.setNewsSource("yidian");
        attributeValues.setVersionCode("20301000");
        attributeValues.setBrandChannel("20");
        attributeValues.setBuuid(230090455319056400l);
        attributeValues.setAid("88af3ae756a68f8d");
        attributeValues.setPkg("com.coloros.yoli");
        reqProperty.setAttributeValues(attributeValues);

        reqProperty.getAttachments().put("traceid", "cc0ecd2a7cbd464fb2f8ce1e2cd5caf1");
        reqProperty.getAttachments().put("content-length", "0");
        reqProperty.getAttachments().put("bc", "OPPO");
        reqProperty.getAttachments().put("duid", "");
        reqProperty.getAttachments().put("rv", "PCAM00_11_A.25");
        reqProperty.getAttachments().put("x-proto", "http");
        reqProperty.getAttachments().put("pcba", "001809709720033000009100");
        reqProperty.getAttachments().put("buuid", "230090455319056384");
        reqProperty.getAttachments().put("nt", "wifi");
        reqProperty.getAttachments().put("t-domain", "ivideo.test-browser.wanyol.com");
        reqProperty.getAttachments().put("cov", "V6.0.1");
        reqProperty.getAttachments().put("pkg", "com.coloros.yoli");
        reqProperty.getAttachments().put("uuid", "6554db34e5f894adbfd3653716973ebd");
        reqProperty.getAttachments().put("bvc", "20301000");
        reqProperty.getAttachments().put("br", "");
        reqProperty.getAttachments().put("dv", "PADM00");
        reqProperty.getAttachments().put("bv", "20.3.1.0der");
        reqProperty.getAttachments().put("request_receive_time", "1586256655138");
        reqProperty.getAttachments().put("host", "ivideo.test-browser.wanyol.com");
        reqProperty.getAttachments().put("sl", "zh-CN");
        reqProperty.getAttachments().put("content-type", "application/pb");
        reqProperty.getAttachments().put("connection", "keep-alive");
        reqProperty.getAttachments().put("cache-control", "no-cache");
        reqProperty.getAttachments().put("ss", "320*676");
        reqProperty.getAttachments().put("mp", "China Mobie");
        reqProperty.getAttachments().put("channelStrategy", "");
        reqProperty.getAttachments().put("level", "1.1.2");
        reqProperty.getAttachments().put("ov", "Android9");
        reqProperty.getAttachments().put("x-forwarded-for", "**************");
        reqProperty.getAttachments().put("response-format", "pb");
        reqProperty.getAttachments().put("mpo", "unknown Operator");
        reqProperty.getAttachments().put("accept", "*/*");
        reqProperty.getAttachments().put("x-real-ip", "**************");
        reqProperty.getAttachments().put("r", "CN");
        reqProperty.getAttachments().put("x-kkbrowser-ua", "605c91eeba04d097c5559cbb062f813dec0cffe75445592e2171441f0b43399842eb5c49a34fee6963c7ec2b756be0aa510342ec06054330abc20f234b9c77ccb6cc851f9e32f479493bfd8b1f9927359ed34c25bbfcecfb4f18e54b58632e017f82364ae4c1dba4c8931141f8d156eed29f0c0e77f9c5d1af4f310115eecb23377e7b6f381d6c591700d01fcad65cb47d1a46ae41db71aae14df813c63c81bde75e550670d3a7e0689c34e22a09f218f9b634bad77fc3ef55e4b5cd27e93fa0ff679a49a8ace9f246e1ce4d47745a75543e955f241de844e3670f5107246b6a4397f9e1f747c93844968378d261546030e4e74d61c2f760585773ba95a596591537edf070659324d5bf1161120613f660c72d0cbf7ada74cfa9a300ccf74eeea604bbcdcd912d1657d4357ddc2de6f830d6cdf10db6ff297c0ecbb553b7025bcf9b1a56bb647419afaf05e7bf08e911e7556667360bbf008b5c91d73e3c7a2df4effc0469145bfd6224696553459bc9cd709a653eaf4497e960d1d0681ada5426974d2755e661e4");
        reqProperty.getAttachments().put("ouid", "");
        reqProperty.getAttachments().put("av", "20.3.1.0der");
        reqProperty.getAttachments().put("x-user-ip", "**************");
        reqProperty.getAttachments().put("covc", "12");
        reqProperty.getAttachments().put("pi", "1080*2280");
        reqProperty.getAttachments().put("imei", "867559045441455");
        reqProperty.getAttachments().put("aid", "88af3ae756a68f8d");
        reqProperty.getAttachments().put("accept-encoding", "gzip,deflate");

        this.reqProperty = reqProperty;
    }

    public FeedsListReqProperty build() {
        return this.reqProperty;
    }
}
