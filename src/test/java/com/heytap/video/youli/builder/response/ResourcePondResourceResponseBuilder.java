package com.heytap.video.youli.builder.response;

import org.apache.commons.io.FileUtils;

import java.net.URL;
import java.nio.charset.StandardCharsets;

public class ResourcePondResourceResponseBuilder {

    public static String build() {
        URL url = ResourcePondResourceResponseBuilder.class.getClassLoader().getResource("json/resource_pond_resource_success.json");
        String jsonStr = null;
        try {
            jsonStr = FileUtils.readFileToString(FileUtils.toFile(url), StandardCharsets.UTF_8);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return jsonStr;
    }
}
