package com.heytap.video.youli.builder.request;

import com.oppo.browser.common.app.lib.cookie.Cookie;
import com.oppo.browser.common.app.lib.cookie.UserInfo;
import com.oppo.browser.strategy.model.AttributeValues;
import com.oppo.browser.video.common.pubobj.resource.ListBaseReqProperty;
import org.mockito.Mockito;

import static org.mockito.Mockito.when;

/**
 * ListBaseReqProperty 测试数据构建器
 *
 * <p>使用 Builder 模式构建 {@link ListBaseReqProperty} 测试对象，
 * 简化测试数据的创建和维护，提供链式调用的流畅接口。</p>
 *
 * <p>该构建器提供了合理的默认值，可以通过链式调用来覆盖特定字段，
 * 特别适用于单元测试中需要构建不同场景测试数据的情况。</p>
 *
 * <h3>使用示例：</h3>
 * <pre>{@code
 * ListBaseReqProperty reqProperty = new ListBaseReqPropertyBuilder()
 *     .withRequestId("test_request_id")
 *     .withDetailStyle(3)
 *     .withRecType(RecTypeConstant.H5)
 *     .withOffset(10)
 *     .withNullCookie()
 *     .build();
 * }</pre>
 *
 * <h3>默认值设置：</h3>
 * <ul>
 *   <li>requestId: "default_request_id"</li>
 *   <li>detailStyle: 1</li>
 *   <li>recType: "normal"</li>
 *   <li>offset: 0</li>
 *   <li>network: "wifi"</li>
 *   <li>phone: "OPPO_R15"</li>
 *   <li>clientVersion: "4.1.0"</li>
 *   <li>ip: "***********"</li>
 *   <li>username: "default_username"</li>
 * </ul>
 *
 * <AUTHOR> Assistant
 * @version 1.0
 * @since 2025-01-01
 * @see ListBaseReqProperty
 * @see AttributeValues
 */
public class ListBaseReqPropertyBuilder {

    /**
     * 被构建的 ListBaseReqProperty 对象
     */
    private ListBaseReqProperty reqProperty;

    /**
     * 构造函数，初始化默认的测试数据
     *
     * <p>设置所有必要字段的合理默认值，包括：</p>
     * <ul>
     *   <li>基础请求参数</li>
     *   <li>用户属性信息</li>
     *   <li>Mock 的 Cookie 对象</li>
     * </ul>
     */
    public ListBaseReqPropertyBuilder() {
        reqProperty = new ListBaseReqProperty();
        
        // 设置默认值
        reqProperty.setRequestId("default_request_id");
        reqProperty.setDetailStyle(1);
        reqProperty.setRecType("normal");
        reqProperty.setFromId("default_from_id");
        reqProperty.setOffset(0);
        reqProperty.setDownTimes(1);
        reqProperty.setUpTimes(0);
        reqProperty.setArea("{\"country\":\"China\",\"province\":\"Beijing\",\"city\":\"Beijing\"}");
        reqProperty.setDocid("default_docid");
        reqProperty.setPageID("default_page_id");
        reqProperty.setSpageID("default_spage_id");
        reqProperty.setMediaNo("default_media_no");
        reqProperty.setLimit(10);
        reqProperty.setPage(1);

        // 设置默认的 AttributeValues
        AttributeValues attributeValues = new AttributeValues();
        attributeValues.setNetwork("wifi");
        attributeValues.setPhone("OPPO_R15");
        attributeValues.setClientFullBrowserVersion("4.1.0");
        attributeValues.setIp("***********");
        attributeValues.setBuuid(123456789L);
        attributeValues.setImei("default_imei");
        reqProperty.setAttributeValues(attributeValues);

        // 设置默认的 Cookie
        Cookie cookie = Mockito.mock(Cookie.class);
        UserInfo cookieInfo = Mockito.mock(UserInfo.class);
        when(cookie.getInfo()).thenReturn(cookieInfo);
        when(cookieInfo.getUn()).thenReturn("default_username");
        when(cookieInfo.getBuuid()).thenReturn(987654321L);
        reqProperty.setScookie(cookie);
    }

    /**
     * 设置请求ID
     *
     * @param requestId 请求ID，用于链路追踪
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withRequestId(String requestId) {
        reqProperty.setRequestId(requestId);
        return this;
    }

    /**
     * 设置详情样式
     *
     * @param detailStyle 详情样式，3表示2N详情页
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withDetailStyle(Integer detailStyle) {
        reqProperty.setDetailStyle(detailStyle);
        return this;
    }

    /**
     * 设置推荐类型
     *
     * @param recType 推荐类型，如 "normal"、RecTypeConstant.H5 等
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withRecType(String recType) {
        reqProperty.setRecType(recType);
        return this;
    }

    /**
     * 设置来源ID
     *
     * @param fromId 来源ID，标识请求来源
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withFromId(String fromId) {
        reqProperty.setFromId(fromId);
        return this;
    }

    /**
     * 设置偏移量
     *
     * @param offset 偏移量，0表示下拉刷新，大于0表示翻页
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withOffset(Integer offset) {
        reqProperty.setOffset(offset);
        return this;
    }

    /**
     * 设置下拉次数
     *
     * @param downTimes 下拉刷新次数
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withDownTimes(Integer downTimes) {
        reqProperty.setDownTimes(downTimes);
        return this;
    }

    /**
     * 设置上滑次数
     *
     * @param upTimes 上滑翻页次数
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withUpTimes(Integer upTimes) {
        reqProperty.setUpTimes(upTimes);
        return this;
    }

    /**
     * 设置地理位置信息
     *
     * @param area 地理位置JSON字符串，包含国家、省份、城市信息
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withArea(String area) {
        reqProperty.setArea(area);
        return this;
    }

    /**
     * 设置文档ID
     *
     * @param docid 文档ID，用于相关推荐
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withDocid(String docid) {
        reqProperty.setDocid(docid);
        return this;
    }

    /**
     * 设置页面ID
     *
     * @param pageID 页面ID，如 "push_detail_2N" 等特殊页面标识
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withPageID(String pageID) {
        reqProperty.setPageID(pageID);
        return this;
    }

    /**
     * 设置上级页面ID
     *
     * @param spageID 上级页面ID，用于推荐扩展参数
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withSpageID(String spageID) {
        reqProperty.setSpageID(spageID);
        return this;
    }

    /**
     * 设置媒体号
     *
     * @param mediaNo 媒体号ID，用于媒体号相关推荐
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withMediaNo(String mediaNo) {
        reqProperty.setMediaNo(mediaNo);
        return this;
    }

    /**
     * 设置网络类型
     *
     * @param network 网络类型，如 "wifi"、"4g"、"5g" 等
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withNetwork(String network) {
        reqProperty.getAttributeValues().setNetwork(network);
        return this;
    }

    /**
     * 设置手机型号
     *
     * @param phone 手机型号，如 "OPPO_R15"、"iPhone_12" 等
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withPhone(String phone) {
        reqProperty.getAttributeValues().setPhone(phone);
        return this;
    }

    /**
     * 设置客户端版本
     *
     * @param clientVersion 客户端完整版本号
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withClientVersion(String clientVersion) {
        reqProperty.getAttributeValues().setClientFullBrowserVersion(clientVersion);
        return this;
    }

    /**
     * 设置IP地址
     *
     * @param ip 客户端IP地址
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withIp(String ip) {
        reqProperty.getAttributeValues().setIp(ip);
        return this;
    }

    /**
     * 设置用户唯一标识
     *
     * @param buuid 用户唯一标识ID
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withBuuid(Long buuid) {
        reqProperty.getAttributeValues().setBuuid(buuid);
        return this;
    }

    /**
     * 设置设备IMEI
     *
     * @param imei 设备IMEI号
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withImei(String imei) {
        reqProperty.getAttributeValues().setImei(imei);
        return this;
    }

    /**
     * 设置Cookie中的用户名
     *
     * @param username Cookie中的用户名
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withUsername(String username) {
        Cookie cookie = reqProperty.getScookieIgnoreException();
        if (cookie != null && cookie.getInfo() != null) {
            when(cookie.getInfo().getUn()).thenReturn(username);
        }
        return this;
    }

    /**
     * 设置Cookie中的用户唯一标识
     *
     * @param cookieBuuid Cookie中的用户唯一标识
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withCookieBuuid(Long cookieBuuid) {
        Cookie cookie = reqProperty.getScookieIgnoreException();
        if (cookie != null && cookie.getInfo() != null) {
            when(cookie.getInfo().getBuuid()).thenReturn(cookieBuuid);
        }
        return this;
    }

    /**
     * 设置Cookie为null，用于测试无Cookie的场景
     *
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withNullCookie() {
        reqProperty.setScookie(null);
        return this;
    }

    /**
     * 设置地理位置信息为null，用于测试无地理位置的场景
     *
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withNullArea() {
        reqProperty.setArea(null);
        return this;
    }

    /**
     * 设置媒体号为null，用于测试无媒体号的场景
     *
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withNullMediaNo() {
        reqProperty.setMediaNo(null);
        return this;
    }

    /**
     * 设置网络类型为null，用于测试无网络信息的场景
     *
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withNullNetwork() {
        reqProperty.getAttributeValues().setNetwork(null);
        return this;
    }

    /**
     * 设置手机型号为null，用于测试无设备信息的场景
     *
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withNullPhone() {
        reqProperty.getAttributeValues().setPhone(null);
        return this;
    }

    /**
     * 设置客户端版本为null，用于测试无版本信息的场景
     *
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withNullClientVersion() {
        reqProperty.getAttributeValues().setClientFullBrowserVersion(null);
        return this;
    }

    /**
     * 设置IP地址为null，用于测试无IP信息的场景
     *
     * @return 当前构建器实例，支持链式调用
     */
    public ListBaseReqPropertyBuilder withNullIp() {
        reqProperty.getAttributeValues().setIp(null);
        return this;
    }

    /**
     * 构建最终的 ListBaseReqProperty 对象
     *
     * @return 构建完成的 ListBaseReqProperty 实例
     */
    public ListBaseReqProperty build() {
        return reqProperty;
    }
}
