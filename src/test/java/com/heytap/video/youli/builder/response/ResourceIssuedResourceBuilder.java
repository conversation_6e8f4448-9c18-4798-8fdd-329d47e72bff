package com.heytap.video.youli.builder.response;

import org.apache.commons.io.FileUtils;

import java.net.URL;
import java.nio.charset.StandardCharsets;

public class ResourceIssuedResourceBuilder {

    public static String buildRsp() {
        URL url = ResourceIssuedResourceBuilder.class.getClassLoader().getResource("json/resource_issued_resource.json");
        try {
            return FileUtils.readFileToString(FileUtils.toFile(url), StandardCharsets.UTF_8);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
