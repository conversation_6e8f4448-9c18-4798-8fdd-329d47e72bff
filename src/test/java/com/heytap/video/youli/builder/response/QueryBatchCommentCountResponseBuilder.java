package com.heytap.video.youli.builder.response;

import org.apache.commons.io.FileUtils;

import java.net.URL;
import java.nio.charset.StandardCharsets;

public class QueryBatchCommentCountResponseBuilder {

    public static String build() {
        URL url = QueryBatchCommentCountResponseBuilder.class.getClassLoader().getResource("json/comment_queryBatchCommentCount.json");
        String jsonStr = null;
        try {
            jsonStr = FileUtils.readFileToString(FileUtils.toFile(url), StandardCharsets.UTF_8);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return jsonStr;
    }
}
